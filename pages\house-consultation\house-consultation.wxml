<!--pages/house-consultation/house-consultation.wxml-->
<view class="container">
  <!-- 购房咨询卡片 -->
  <image class="lop" src="/image/xingzhouzixun.png" mode="widthFix"></image>
  <!-- 价格信息 -->
  <view class="price-section">
    <view class="price-info">
      <text class="service-type">{{serviceInfo.serviceType}}</text>
      <view class="price-container">
        <text class="price-symbol">¥</text>
        <text class="price-amount" >{{serviceInfo.price}}</text>
      </view>
      <text class="buy-count" >{{serviceInfo.buyCount}}</text>
    </view>
  </view>

  <!-- 销售导语 -->
  <view class="sales-description">
    <view class="description-title">销售导语</view>
    <view class="description-content">
      <text class="description-text" >在深圳，买房是一件重要且困难的事，且越来越难，大多数家庭人，在现在这个节点走入市场，往往都需要专业的买房顾问或，过程中生的问题往往也是八门。
      
      我们在会谈中就碰到了不少，包括但不限于:
      ☆ 我想按揭是XXX，买哪个楼盘的性价比？
      ☆ 我想上了一个学区，有什么楼盘推荐会适合？
      ☆ 买二手房好还是新房好？准备首选如何操作？
      ☆ 孩子准备上学，有没有合适的学位房推荐？
      
      虽然问题很多，但其实不难，如果对楼市有系统的认知，消除信息差，就可以得到一个相对优解。不过大多数人没有足够的精力做到这一点，房子偏扁又是一个复购率很低的产品，一旦买错，后悔五年八年都很常见。
      
      所以，如果你在买房路上也面临困感，不妨考虑一下行舟咨询。俺们立场客观，所有建议只为你考虑。一次简单面聊， 一定能让你少踩不少坑.</text>
    </view>
  </view>

  <!-- 咨询流程 -->
  <view class="consultation-process">
    <view class="process-title">咨询流程</view>
    <view class="process-item">
    <text class="wq">咨询流程</text>：此页面下单购买→预约时间地点→收到置业情况表，按表格填写基本情况→到期赴约(建议带家属) 
    <view></view>
   <text class="we">时间地点</text>：单次咨询时长一般2小时左右，但不严格限时; 地点在福田区车公庙附近
   <view></view>
   <text class="wp">后续服务</text>：后续有问题可随时沟通，至买到房为止
   
 
    </view>
  </view>

  <!-- 购买区域 -->
  <view class="purchase-section">
   
    <!-- 购买按钮 -->
    <view class="purchase-button" bindtap="onPurchase">
      立即购买
    </view>
  </view>

  <!-- 第一步：购买弹窗 -->
  <view class="modal-overlay" wx:if="{{showPurchaseModal}}" bindtap="closePurchaseModal">
    <view class="purchase-modal" catchtap="">
      <!-- 拖拽指示器 -->
      <view class="modal-handle"></view>
      <!-- 关闭按钮 -->
      <view class="modal-close" bindtap="closePurchaseModal">×</view>

      <view class="modal-content">
        <!-- 左侧图片区域 -->
        <view class="modal-left">
          <image src="/image/xingzhouzixun.png" mode="aspectFill"></image>
        </view>

        <!-- 右侧内容区域 -->
        <view class="modal-right">
          <!-- 价格显示 -->
          <view class="modal-price">
            <text class="price-symbol">¥</text>
            <text class="price-amount">498.00</text>
          </view>

          <!-- 买房顾问 -->
          <view class="modal-section">
            <text class="section-title">买房顾问</text>
            <view class="section-tag">行舟</view>
          </view>

          <!-- 咨询服务 -->
          <view class="modal-section">
            <text class="section-title">咨询服务</text>
            <view class="section-tag">房产深度咨询</view>
          </view>

          <!-- 服务期限 -->
          <view class="modal-section">
            <text class="section-title">服务期限</text>
            <view class="section-tag">长期服务，至买到房为止</view>
          </view>
        </view>
      </view>

      <!-- 立即购买按钮 -->
      <view class="modal-purchase-btn" bindtap="confirmPurchase">
        立即购买
      </view>
    </view>
  </view>

  <!-- 第二步：支付确认弹窗 -->
  <view class="modal-overlay" wx:if="{{showPaymentModal}}" bindtap="closePaymentModal">
    <view class="payment-modal" catchtap="">
      <view class="modal-handle"></view>
      <view class="modal-close" bindtap="closePaymentModal">×</view>

      <view class="payment-content">
        <view class="payment-title">支付确认</view>
        <view class="payment-amount">
          <text>支付金额：</text>
          <text class="amount">¥{{purchaseInfo.price}}</text>
        </view>
        <view class="payment-service">房产深度咨询服务</view>

        <view class="payment-buttons">
          <view class="cancel-btn" bindtap="closePaymentModal">取消</view>
          <view class="confirm-btn" bindtap="confirmPayment">确认支付</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 第三步：二维码弹窗 -->
  <view class="modal-overlay" wx:if="{{showQrcodeModal}}" bindtap="closeQrcodeModal">
    <view class="qrcode-modal" catchtap="">
      <view class="modal-handle"></view>
      <view class="modal-close" bindtap="closeQrcodeModal">×</view>

      <view class="qrcode-content">
        <view class="qrcode-title">购买成功</view>
        <view class="qrcode-subtitle">请扫描二维码添加微信群</view>

        <view class="qrcode-image">
          <image src="/image/maifang.png" mode="aspectFit"></image>
        </view>

        <view class="qrcode-tips">
          <text>扫描二维码加入专属咨询群</text>
          <text>专业顾问将为您提供一对一服务</text>
        </view>

        <view class="qrcode-close-btn" bindtap="closeQrcodeModal">
          我知道了
        </view>
      </view>
    </view>
  </view>

</view>
