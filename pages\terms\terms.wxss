/**terms.wxss**/
.container {
  width: 750rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部状态栏 */
.status-bar {
  width: 750rpx;
  height: 88rpx;
  background-color: #000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.battery-icon {
  width: 48rpx;
  height: 24rpx;
  border: 2rpx solid white;
  border-radius: 4rpx;
  position: relative;
}

.battery-icon::after {
  content: '';
  position: absolute;
  right: -6rpx;
  top: 6rpx;
  width: 4rpx;
  height: 12rpx;
  background-color: white;
  border-radius: 0 2rpx 2rpx 0;
}

/* 页面标题区域 */
.page-header {
  width: 750rpx;
  height: 120rpx;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-icons {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.icon-dots {
  display: flex;
  gap: 6rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #999;
  border-radius: 50%;
}

.icon-circle {
  width: 40rpx;
  height: 40rpx;
  background-color: #333;
  border-radius: 50%;
}

/* 标签页切换 */
.tab-container {
  width: 750rpx;
  height: 100rpx;
  background-color: white;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
  margin-top: -210rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100rpx;
}

.tab-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #FF4444;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #FF4444;
  border-radius: 3rpx;
}

/* 内容区域 */
.content-container {
  width: 750rpx;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
}

.content-panel {
  width: 100%;
}
.poo{
  margin-top: -980rpx;
}
.poi{
  margin-top: -980rpx;
}
.content-panel.show {
  display: block;
}

.content-panel.hide {
  display: none;
}

/* 内容项目 */
.content-item {
  width: 100%;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  height: 80rpx;
  
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.item-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.3;
}

.item-date {
  font-size: 28rpx;
  color: #999;
  position: relative;
  top: 55rpx;
  left: -600rpx;
  text-align: left;
}

/* 点击效果 */
.content-item:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
  transition: all 0.1s ease;
}
