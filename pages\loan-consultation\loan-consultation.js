// pages/loan-consultation/loan-consultation.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const util = require('../../utils/util.js')

Page({
  data: {
    // 贷款政策信息
    policyInfo: {
      title: '最新房贷政策',
      updateTime: '2024-01-15',
      highlights: [
        '首套房首付比例最低20%',
        '二套房首付比例最低30%',
        '公积金贷款利率3.1%',
        '商业贷款利率4.2%起'
      ]
    },

    // 贷款种类选项
    loanTypes: ['商业贷款', '公积金贷款', '组合贷款'],
    loanTypeIndex: 0,

    // 贷款期限选项
    loanTerms: ['10年', '15年', '20年', '25年', '30年'],
    loanTermIndex: 4,

    // 表单数据
    loanAmount: '',
    expectedRate: '',
    contactName: '',
    contactPhone: '',
    remarks: '',

    // 资质预审数据
    monthlyIncome: '',
    hasHouse: false,
    creditScore: '',

    // FAQ数据
    faqList: [
      {
        question: '首付比例要求是多少？',
        answer: '首套房最低20%，二套房最低30%，具体比例根据城市政策和银行要求有所不同。',
        expanded: false
      },
      {
        question: '银行流水证明标准',
        answer: '一般需要提供近6个月的银行流水，月收入应为月供的2倍以上，流水要体现稳定的收入来源。',
        expanded: false
      },
      {
        question: '征信要求说明',
        answer: '个人征信报告不能有逾期记录，信用卡使用率不宜过高，近2年内不能有连续3次或累计6次逾期。',
        expanded: false
      },
      {
        question: '贷款审批时间',
        answer: '一般商业贷款审批需要15-30个工作日，公积金贷款需要30-45个工作日，具体时间因银行而异。',
        expanded: false
      },
      {
        question: '提前还款政策',
        answer: '大部分银行支持提前还款，但可能收取一定手续费，建议贷款满1年后再考虑提前还款。',
        expanded: false
      }
    ],

    // 贷款流程步骤
    loanSteps: [
      { step: 1, title: '准备材料', desc: '身份证、收入证明、银行流水等' },
      { step: 2, title: '选择银行', desc: '比较各银行利率和政策' },
      { step: 3, title: '提交申请', desc: '填写贷款申请表并提交材料' },
      { step: 4, title: '银行审核', desc: '银行审核资质和材料' },
      { step: 5, title: '签订合同', desc: '审核通过后签订贷款合同' },
      { step: 6, title: '放款到账', desc: '银行放款到指定账户' }
    ],

    // 提交状态
    canSubmit: false,
    showQualificationResult: false,
    qualificationResult: null,

    // 提交咨询弹窗状态
    showSubmitModal: false,
    submitSuccess: false,
    consultationId: ''
  },

  onLoad: function (options) {
    console.log('贷款咨询页面加载');
    this.checkCanSubmit();
  },

  // 贷款种类选择
  onLoanTypeChange: function(e) {
    this.setData({
      loanTypeIndex: parseInt(e.detail.value)
    });
    this.checkCanSubmit();
  },

  // 贷款期限选择
  onLoanTermChange: function(e) {
    this.setData({
      loanTermIndex: parseInt(e.detail.value)
    });
    this.checkCanSubmit();
  },

  // 贷款额度输入
  onLoanAmountInput: function(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      loanAmount: value
    });
    this.checkCanSubmit();
  },

  // 期望利率输入
  onExpectedRateInput: function(e) {
    const value = this.validateRateInput(e.detail.value);
    this.setData({
      expectedRate: value
    });
    this.checkCanSubmit();
  },

  // 联系人输入
  onContactNameInput: function(e) {
    this.setData({
      contactName: e.detail.value
    });
    this.checkCanSubmit();
  },

  // 联系电话输入
  onContactPhoneInput: function(e) {
    const value = this.validatePhoneInput(e.detail.value);
    this.setData({
      contactPhone: value
    });
    this.checkCanSubmit();
  },

  // 备注输入
  onRemarksInput: function(e) {
    this.setData({
      remarks: e.detail.value
    });
  },

  // 月收入输入
  onMonthlyIncomeInput: function(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      monthlyIncome: value
    });
  },

  // 是否有房切换
  onHasHouseChange: function(e) {
    this.setData({
      hasHouse: e.detail.value
    });
  },

  // 信用评分输入
  onCreditScoreInput: function(e) {
    const value = this.validateCreditScore(e.detail.value);
    this.setData({
      creditScore: value
    });
  },

  // 验证数字输入
  validateNumberInput: function(value) {
    let cleanValue = value.replace(/[^\d.]/g, '');
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }
    if (parts.length === 2 && parts[1].length > 2) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
    }
    return cleanValue;
  },

  // 验证利率输入
  validateRateInput: function(value) {
    let cleanValue = value.replace(/[^\d.]/g, '');
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }
    if (parts.length === 2 && parts[1].length > 2) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
    }
    // 限制利率范围 0-20%
    const rate = parseFloat(cleanValue);
    if (rate > 20) {
      cleanValue = '20';
    }
    return cleanValue;
  },

  // 验证手机号输入
  validatePhoneInput: function(value) {
    return value.replace(/[^\d]/g, '');
  },

  // 验证信用评分
  validateCreditScore: function(value) {
    let cleanValue = value.replace(/[^\d]/g, '');
    const score = parseInt(cleanValue);
    if (score > 850) {
      cleanValue = '850';
    }
    return cleanValue;
  },

  // 检查是否可以提交
  checkCanSubmit: function() {
    const { loanAmount, contactName, contactPhone } = this.data;
    
    const canSubmit = loanAmount.trim() !== '' && 
                     contactName.trim() !== '' && 
                     contactPhone.trim() !== '' &&
                     contactPhone.length >= 11;
    
    this.setData({
      canSubmit: canSubmit
    });
  },

  // FAQ展开/收起
  toggleFAQ: function(e) {
    const index = e.currentTarget.dataset.index;
    const faqList = this.data.faqList;
    faqList[index].expanded = !faqList[index].expanded;
    this.setData({
      faqList: faqList
    });
  },

  // 资质预审
  performQualificationCheck: function() {
    const { monthlyIncome, hasHouse, creditScore } = this.data;
    
    if (!monthlyIncome) {
      util.showError('请输入月收入');
      return;
    }

    util.showLoading('评估中...');
    
    // 模拟评估逻辑
    setTimeout(() => {
      const income = parseFloat(monthlyIncome);
      const credit = parseInt(creditScore) || 700;
      
      let result = {
        canLoan: true,
        maxAmount: 0,
        recommendedRate: '4.2%',
        suggestions: []
      };

      // 简单的评估逻辑
      result.maxAmount = Math.floor(income * 0.5 * 12 * 20); // 月收入的50%作为月供，20年
      
      if (hasHouse) {
        result.maxAmount *= 0.7; // 二套房降低额度
        result.recommendedRate = '4.9%';
        result.suggestions.push('二套房政策，首付比例需30%以上');
      }
      
      if (credit < 650) {
        result.canLoan = false;
        result.suggestions.push('信用评分偏低，建议先改善征信记录');
      } else if (credit < 700) {
        result.maxAmount *= 0.8;
        result.suggestions.push('信用评分一般，可能影响贷款额度');
      }
      
      if (income < 5000) {
        result.suggestions.push('收入偏低，建议提供额外收入证明');
      }

      util.hideLoading();
      this.setData({
        showQualificationResult: true,
        qualificationResult: result
      });
    }, 2000);
  },

  // 关闭资质预审结果
  closeQualificationResult: function() {
    this.setData({
      showQualificationResult: false
    });
  },

  // 提交贷款咨询 - 显示弹窗
  onSubmit: function() {
    if (!this.data.canSubmit) {
      util.showError('请完善必填信息');
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.data.contactPhone)) {
      util.showError('请输入正确的手机号');
      return;
    }

    // 模拟提交处理
    util.showLoading('提交中...');

    setTimeout(() => {
      util.hideLoading();

      // 生成咨询编号
      const consultationId = 'LC' + Date.now();

      // 显示成功弹窗
      this.setData({
        showSubmitModal: true,
        submitSuccess: true,
        consultationId: consultationId
      });
    }, 1500);
  },

  // 关闭提交弹窗
  closeSubmitModal: function() {
    this.setData({
      showSubmitModal: false,
      submitSuccess: false,
      consultationId: ''
    });
  },

  // 复制咨询编号
  copyConsultationId: function() {
    wx.setClipboardData({
      data: this.data.consultationId,
      success: () => {
        util.showSuccess('咨询编号已复制');
      }
    });
  },

  // 保存二维码
  saveQRCode: function() {
    wx.showModal({
      title: '保存二维码',
      content: '长按二维码图片可以保存到相册',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 提交贷款咨询数据
  submitLoanConsultation: function(submitData) {
    util.showLoading('提交中...');

    // 模拟API调用
    setTimeout(() => {
      util.hideLoading();

      // 模拟成功响应
      const consultationId = 'LC' + Date.now();

      wx.showModal({
        title: '提交成功',
        content: `您的贷款咨询已提交成功！\n咨询编号：${consultationId}\n我们的专业顾问将在24小时内联系您。`,
        showCancel: false,
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.resetForm();
          }
        }
      });
    }, 1500);
  },

  // 重置表单
  resetForm: function() {
    this.setData({
      loanTypeIndex: 0,
      loanTermIndex: 4,
      loanAmount: '',
      expectedRate: '',
      contactName: '',
      contactPhone: '',
      remarks: '',
      monthlyIncome: '',
      hasHouse: false,
      creditScore: '',
      canSubmit: false,
      showQualificationResult: false,
      qualificationResult: null,
      showSubmitModal: false,
      submitSuccess: false,
      consultationId: ''
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 分享
  onShareAppMessage: function() {
    return {
      title: '深房雷达 - 贷款咨询',
      path: '/pages/loan-consultation/loan-consultation',
      imageUrl: '/image/daikuan.png'
    };
  }
});
