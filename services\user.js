// services/user.js - 用户服务
const api = require('../config/api.js');

// 模拟用户数据
const mockUser = {
  id: 'user_' + Date.now(),
  username: '测试用户',
  phone: '13800138000',
  avatar: '',
  isLoggedIn: false
};

/**
 * 检查登录状态
 */
function checkLoginStatus() {
  try {
    const userInfo = wx.getStorageSync('userInfo');
    const accessToken = wx.getStorageSync('accessToken');
    
    if (userInfo && accessToken) {
      console.log('用户已登录:', userInfo);
      return true;
    }
    
    console.log('用户未登录');
    return false;
  } catch (error) {
    console.error('检查登录状态失败:', error);
    return false;
  }
}

/**
 * 获取本地用户信息
 */
function getLocalUserInfo() {
  try {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      return userInfo;
    }
    
    // 如果没有本地用户信息，返回模拟用户信息
    console.log('返回模拟用户信息');
    return mockUser;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return mockUser;
  }
}

/**
 * 保存用户信息到本地
 */
function saveUserInfo(userInfo) {
  try {
    wx.setStorageSync('userInfo', userInfo);
    console.log('用户信息保存成功:', userInfo);
    return true;
  } catch (error) {
    console.error('保存用户信息失败:', error);
    return false;
  }
}

/**
 * 保存访问令牌
 */
function saveAccessToken(token) {
  try {
    wx.setStorageSync('accessToken', token);
    console.log('访问令牌保存成功');
    return true;
  } catch (error) {
    console.error('保存访问令牌失败:', error);
    return false;
  }
}

/**
 * 模拟登录
 */
function mockLogin() {
  const mockToken = 'mock_token_' + Date.now();
  const mockUserInfo = {
    ...mockUser,
    isLoggedIn: true
  };
  
  saveUserInfo(mockUserInfo);
  saveAccessToken(mockToken);
  
  console.log('模拟登录成功');
  return {
    userInfo: mockUserInfo,
    token: mockToken
  };
}

/**
 * 登出
 */
function logout() {
  try {
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('accessToken');
    console.log('用户登出成功');
    return true;
  } catch (error) {
    console.error('登出失败:', error);
    return false;
  }
}

/**
 * 用户登录
 */
async function login(loginData) {
  try {
    console.log('开始登录:', loginData);
    
    // 模拟API调用
    const response = await api.post(api.API.USER_LOGIN, loginData);
    
    if (response.code === 200) {
      const { userInfo, token } = response.data;
      saveUserInfo(userInfo);
      saveAccessToken(token);
      return response;
    } else {
      throw new Error(response.message || '登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
    // 如果API调用失败，使用模拟登录
    return {
      code: 200,
      message: '模拟登录成功',
      data: mockLogin()
    };
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo() {
  try {
    const accessToken = wx.getStorageSync('accessToken');
    if (!accessToken) {
      throw new Error('未登录');
    }
    
    const response = await api.get(api.API.USER_INFO, {}, {
      'Authorization': `Bearer ${accessToken}`
    });
    
    if (response.code === 200) {
      saveUserInfo(response.data);
      return response;
    } else {
      throw new Error(response.message || '获取用户信息失败');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    // 返回本地用户信息
    return {
      code: 200,
      message: '获取本地用户信息',
      data: getLocalUserInfo()
    };
  }
}

module.exports = {
  checkLoginStatus,
  getLocalUserInfo,
  saveUserInfo,
  saveAccessToken,
  mockLogin,
  logout,
  login,
  getUserInfo
};
