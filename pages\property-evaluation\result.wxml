<!--pages/property-evaluation/result.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在评估中...</text>
  </view>

  <!-- 评估结果 -->
  <view wx:else class="result-container">
    <!-- 房屋基本信息 -->
    <view class="house-info-card">
      <view class="house-title">{{formData.communityName}}</view>
      <view class="house-details">
        <text class="detail-item">{{formData.houseType}}</text>
        <text class="detail-separator">·</text>
        <text class="detail-item">{{formData.buildingArea}}㎡</text>
        <text class="detail-separator">·</text>
        <text class="detail-item">{{formData.currentFloor}}/{{formData.totalFloor}}层</text>
      </view>
    </view>

    <!-- 估值结果卡片 -->
    <view class="valuation-card">
      <view class="valuation-header">
        <text class="valuation-title">估值结果</text>
        <view class="score-badge">
          <text class="score-text">{{evaluation.totalScore}}分</text>
        </view>
      </view>

      <view class="price-display">
        <view class="price-range">
          <text class="price-number">{{evaluation.minPrice}} - {{evaluation.maxPrice}}</text>
          <text class="price-unit">万元</text>
        </view>
        <view class="avg-price">
          <text class="avg-label">平均估值</text>
          <text class="avg-number">{{evaluation.avgPrice}}万元</text>
        </view>
        <view class="unit-price">
          <text class="unit-label">单价</text>
          <text class="unit-number">{{evaluation.pricePerSqm}}元/㎡</text>
        </view>
      </view>
    </view>

    <!-- 评估因素详情 -->
    <view class="factors-card">
      <view class="factors-header">
        <text class="factors-title">评估因素分析</text>
      </view>

      <view class="factors-list">
        <view wx:for="{{evaluation.factors}}" wx:key="name" class="factor-item">
          <view class="factor-info">
            <text class="factor-name">{{item.name}}</text>
            <text class="factor-weight">权重{{item.weight}}%</text>
          </view>
          <view class="factor-score">
            <view class="score-bar">
             
            </view>
            <text class="score-value">{{item.score}}分</text>
          </view>
          <view class="factor-impact {{item.impact > 0 ? 'positive' : 'negative'}}">
            <text>{{item.impact > 0 ? '+' : ''}}{{item.impact}}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 免责声明 -->
    <view class="disclaimer-card">
      <view class="disclaimer-header">
        <text class="disclaimer-title">免责声明</text>
      </view>
      <view class="disclaimer-content">
        <text class="disclaimer-text">
          1. 本评估结果仅供参考，不构成投资建议
          2. 实际房价受市场供需、政策等多种因素影响
          3. 最终成交价格以实际市场交易为准
          4. 评估算法基于统计数据，可能存在偏差
        </text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button class="action-btn secondary-btn" bindtap="reEvaluate">重新评估</button>
      <button class="action-btn primary-btn" open-type="share">分享结果</button>
    </view>
  </view>
</view>