// config/api.js - API配置文件
const BASE_URL = 'https://api.example.com'; // 模拟API地址

// API接口定义
const API = {
  // 房源出售相关
  HOUSE_SALE_CREATE: '/house/sale/create',
  HOUSE_SALE_LIST: '/house/sale/list',
  HOUSE_SALE_DETAIL: '/house/sale/detail',
  
  // 用户相关
  USER_LOGIN: '/user/login',
  USER_INFO: '/user/info',
  USER_REGISTER: '/user/register',
  
  // 其他接口
  CONSULTATION_CREATE: '/consultation/create',
  PROPERTY_EVALUATION: '/property/evaluation'
};

// 模拟网络请求函数
function request(url, options = {}) {
  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      console.log('模拟API请求:', url, options);
      
      // 模拟成功响应
      const mockResponse = {
        code: 200,
        message: 'success',
        data: {
          id: Date.now(), // 模拟生成的ID
          status: 'success',
          timestamp: new Date().toISOString()
        }
      };
      
      resolve(mockResponse);
    }, 1000);
  });
}

// GET请求
function get(url, params = {}, headers = {}) {
  return request(url, {
    method: 'GET',
    params,
    headers
  });
}

// POST请求
function post(url, data = {}, headers = {}) {
  return request(url, {
    method: 'POST',
    data,
    headers
  });
}

// PUT请求
function put(url, data = {}, headers = {}) {
  return request(url, {
    method: 'PUT',
    data,
    headers
  });
}

// DELETE请求
function del(url, params = {}, headers = {}) {
  return request(url, {
    method: 'DELETE',
    params,
    headers
  });
}

module.exports = {
  BASE_URL,
  API,
  request,
  get,
  post,
  put,
  delete: del
};
