<!--pages/property-evaluation/property-evaluation.wxml-->
<view class="container">
  <!-- 顶部进度条 -->
  <view class="progress-container">
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progressWidth}}%"></view>
    </view>
    <text class="progress-text">{{currentStep}}/{{totalSteps}}</text>
  </view>


  <!-- 表单内容 -->
  <view class="form-content">
    <!-- 第一步：基本信息 -->
    <view wx:if="{{currentStep === 1}}" class="step-content">
      <view class="form-item">
        <text class="form-label">小区名称 *</text>
        <input 
          class="form-input" 
          placeholder="请输入小区名称" 
          value="{{formData.communityName}}"
          data-field="communityName"
          bindinput="onInput"
        />
      </view>

      <view class="form-item">
        <text class="form-label">户型 *</text>
        <picker
          range="{{houseTypeOptions}}"
          value="{{pickerIndexes.houseType}}"
          data-field="houseType"
          data-options="houseTypeOptions"
          bindchange="onPickerChange"
        >
          <view class="picker-display">
            <text class="{{formData.houseType ? 'selected' : 'placeholder'}}" >
              {{formData.houseType || '请选择户型'}}
            </text>
            <text class="arrow">></text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">建筑面积 *</text>
        <view class="input-with-unit">
          <input 
            class="form-input" 
            placeholder="请输入建筑面积" 
            type="digit"
            value="{{formData.buildingArea}}"
            data-field="buildingArea"
            bindinput="onInput"
          />
          <text class="input-unit">平方米</text>
        </view>
      </view>
    </view>

    <!-- 第二步：房屋详情 -->
    <view wx:if="{{currentStep === 2}}" class="step-content">
      <view class="form-item">
        <text class="form-label">房龄 *</text>
        <picker
          range="{{houseAgeOptions}}"
          value="{{pickerIndexes.houseAge}}"
          data-field="houseAge"
          data-options="houseAgeOptions"
          bindchange="onPickerChange"
        >
          <view class="picker-display">
            <text class="{{formData.houseAge ? 'selected' : 'placeholder'}}">
              {{formData.houseAge || '请选择房龄'}}
            </text>
            <text class="arrow">></text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">装修情况 *</text>
        <picker
          range="{{decorationOptions}}"
          value="{{pickerIndexes.decoration}}"
          data-field="decoration"
          data-options="decorationOptions"
          bindchange="onPickerChange"
        >
          <view class="picker-display">
            <text class="{{formData.decoration ? 'selected' : 'placeholder'}}">
              {{formData.decoration || '请选择装修情况'}}
            </text>
            <text class="arrow">></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 第三步：位置信息 -->
    <view wx:if="{{currentStep === 3}}" class="step-content">
      <view class="form-item">
        <text class="form-label">楼层信息 *</text>
        <view class="floor-input">
          <input 
            class="floor-input-item" 
            placeholder="当前楼层" 
            type="number"
            value="{{formData.currentFloor}}"
            data-field="currentFloor"
            bindinput="onInput"
          />
          <text class="floor-separator">/</text>
          <input 
            class="floor-input-item" 
            placeholder="总楼层" 
            type="number"
            value="{{formData.totalFloor}}"
            data-field="totalFloor"
            bindinput="onInput"
          />
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">朝向 *</text>
        <picker
          range="{{orientationOptions}}"
          value="{{pickerIndexes.orientation}}"
          data-field="orientation"
          data-options="orientationOptions"
          bindchange="onPickerChange"
        >
          <view class="picker-display">
            <text class="{{formData.orientation ? 'selected' : 'placeholder'}}">
              {{formData.orientation || '请选择朝向'}}
            </text>
            <text class="arrow">></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 第四步：配套设施 -->
    <view wx:if="{{currentStep === 4}}" class="step-content">
      <view class="form-item">
        <text class="form-label">周边配套</text>
        <view class="multi-select-container">
          <view 
            wx:for="{{facilitiesOptions}}" 
            wx:key="id"
            class="multi-select-item {{item.selected ? 'selected' : ''}}"
            data-field="facilities"
            data-options="facilitiesOptions"
            data-index="{{index}}"
            bindtap="onToggleMultiSelect"
          >
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">房屋现状 *</text>
        <picker
          range="{{houseStatusOptions}}"
          value="{{pickerIndexes.houseStatus}}"
          data-field="houseStatus"
          data-options="houseStatusOptions"
          bindchange="onPickerChange"
        >
          <view class="picker-display">
            <text class="{{formData.houseStatus ? 'selected' : 'placeholder'}}">
              {{formData.houseStatus || '请选择房屋现状'}}
            </text>
            <text class="arrow">></text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">特殊情况</text>
        <view class="multi-select-container">
          <view 
            wx:for="{{specialConditionsOptions}}" 
            wx:key="id"
            class="multi-select-item {{item.selected ? 'selected' : ''}}"
            data-field="specialConditions"
            data-options="specialConditionsOptions"
            data-index="{{index}}"
            bindtap="onToggleMultiSelect"
          >
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 第五步：图片上传 -->
    <view wx:if="{{currentStep === 5}}" class="step-content">
      <view class="upload-section">
        <text class="upload-title">房屋现状照片（最多6张）</text>
        <view class="upload-grid">
          <view 
            wx:for="{{formData.housePhotos}}" 
            wx:key="index"
            class="upload-item"
          >
            <image src="{{item}}" class="upload-image" mode="aspectFill"></image>
            <view class="delete-btn" data-type="housePhotos" data-index="{{index}}" bindtap="deleteImage">×</view>
          </view>
          <view wx:if="{{formData.housePhotos.length < 6}}" class="upload-placeholder" bindtap="chooseImage" data-type="housePhotos">
            <text class="upload-icon">+</text>
            <text class="upload-text">添加照片</text>
          </view>
        </view>
      </view>

      <view class="upload-section">
        <text class="upload-title">户型图（1张）</text>
        <view class="single-upload">
          <view wx:if="{{formData.floorPlan}}" class="upload-item">
            <image src="{{formData.floorPlan}}" class="upload-image" mode="aspectFill"></image>
            <view class="delete-btn" data-type="floorPlan" bindtap="deleteImage">×</view>
          </view>
          <view wx:else class="upload-placeholder" bindtap="chooseImage" data-type="floorPlan">
            <text class="upload-icon">+</text>
            <text class="upload-text">上传户型图</text>
          </view>
        </view>
      </view>

      <view class="upload-section">
        <text class="upload-title">房产证照片（1张）</text>
        <view class="single-upload">
          <view wx:if="{{formData.propertyCard}}" class="upload-item">
            <image src="{{formData.propertyCard}}" class="upload-image" mode="aspectFill"></image>
            <view class="delete-btn" data-type="propertyCard" bindtap="deleteImage">×</view>
          </view>
          <view wx:else class="upload-placeholder" bindtap="chooseImage" data-type="propertyCard">
            <text class="upload-icon">+</text>
            <text class="upload-text">上传房产证</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <button wx:if="{{currentStep > 1}}" class="btn btn-secondary" bindtap="prevStep">上一步</button>
    <button class="btn btn-primary" bindtap="nextStep">
      {{currentStep === totalSteps ? '开始评估' : '下一步'}}
    </button>
  </view>
</view>
