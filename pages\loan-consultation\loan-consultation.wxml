<!--pages/loan-consultation/loan-consultation.wxml-->
<view class="container">
  
  <!-- 贷款政策解读区域 -->
  <view class="policy-section">
    <view class="section-header">
      <image class="header-icon" src="/image/daikuan.png" mode="aspectFit"></image>
      <view class="header-content">
        <text class="section-op">{{policyInfo.title}}</text>
        <text class="update-time">更新时间：{{policyInfo.updateTime}}</text>
      </view>
    </view>
    
    <view class="policy-highlights">
      <view class="highlight-item" wx:for="{{policyInfo.highlights}}" wx:key="index">
        <text class="highlight-dot">•</text>
        <text class="highlight-text">{{item}}</text>
      </view>
    </view>
  </view>



  <!-- 贷款流程指导 -->
  <view class="process-section">
    <view class="section-ti">贷款申请流程</view>
    
    <view class="process-steps">
      <view class="step-item" wx:for="{{loanSteps}}" wx:key="step">
        <view class="step-number">{{item.step}}</view>
        <view class="step-content">
          <text class="step-title">{{item.title}}</text>
          <text class="step-desc">{{item.desc}}</text>
        </view>
        <view class="step-line" wx:if="{{index < loanSteps.length - 1}}"></view>
      </view>
    </view>
  </view>

  <!-- FAQ常见问题区域 -->
  <view class="faq-section">
    <view class="section-title">常见问题</view>
    
    <view class="faq-list">
      <view class="faq-item" wx:for="{{faqList}}" wx:key="index">
        <view class="faq-question" bindtap="toggleFAQ" data-index="{{index}}">
          <text class="question-text">{{item.question}}</text>
          <text class="toggle-icon {{item.expanded ? 'expanded' : ''}}">▼</text>
        </view>
        <view class="faq-answer {{item.expanded ? 'show' : ''}}">
          <text class="answer-text">{{item.answer}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 资质预审功能 -->
  <view class="qualification-section">
    <view class="section-title">贷款资质预审</view>
    
    <view class="form-group">
      <text class="form-label">月收入（元）</text>
      <input class="form-input" type="digit" placeholder="请输入月收入" 
             value="{{monthlyIncome}}" bindinput="onMonthlyIncomeInput" />
    </view>
    
    <view class="form-group">
      <text class="form-label">是否已有房产</text>
      <switch class="form-switch" checked="{{hasHouse}}" bindchange="onHasHouseChange" />
    </view>
    
    <view class="form-group">
      <text class="form-label">信用评分（可选）</text>
      <input class="form-input" type="number" placeholder="300-850" 
             value="{{creditScore}}" bindinput="onCreditScoreInput" />
    </view>
    
    <view class="qualification-btn" bindtap="performQualificationCheck">
      开始预审
    </view>
  </view>

 

 <!-- 贷款需求提交表单 -->
 <view class="form-section">
    <view class="section-title">贷款需求提交</view>
    
    <view class="form-group">
      <text class="form-label">贷款种类 *</text>
      <picker class="form-picker" mode="selector" range="{{loanTypes}}" 
              value="{{loanTypeIndex}}" bindchange="onLoanTypeChange">
        <view class="picker-text">{{loanTypes[loanTypeIndex]}}</view>
      </picker>
    </view>
    
    <view class="form-group">
      <text class="form-label">贷款额度（万元）*</text>
      <input class="form-input" type="digit" placeholder="请输入贷款额度" 
             value="{{loanAmount}}" bindinput="onLoanAmountInput" />
    </view>
    
    <view class="form-group">
      <text class="form-label">期望利率（%）</text>
      <input class="form-input" type="digit" placeholder="如：4.2" 
             value="{{expectedRate}}" bindinput="onExpectedRateInput" />
    </view>
    
    <view class="form-group">
      <text class="form-label">贷款期限 *</text>
      <picker class="form-picker" mode="selector" range="{{loanTerms}}" 
              value="{{loanTermIndex}}" bindchange="onLoanTermChange">
        <view class="picker-text">{{loanTerms[loanTermIndex]}}</view>
      </picker>
    </view>
    
    <view class="form-group">
      <text class="form-label">联系人 *</text>
      <input class="form-input" type="text" placeholder="请输入联系人姓名" 
             value="{{contactName}}" bindinput="onContactNameInput" />
    </view>
    
    <view class="form-group">
      <text class="form-label">联系电话 *</text>
      <input class="form-input" type="number" placeholder="请输入手机号" 
             value="{{contactPhone}}" bindinput="onContactPhoneInput" />
    </view>
    
    <view class="form-group">
      <text class="form-label">备注说明</text>
      <textarea class="form-textarea" placeholder="请输入其他需求或说明" 
                value="{{remarks}}" bindinput="onRemarksInput"></textarea>
    </view>
    
    <view class="submit-btn {{canSubmit ? 'active' : ''}}" bindtap="onSubmit">
      提交咨询
    </view>
  </view>

</view>

<!-- 资质预审结果弹窗 -->
<view class="modal-overlay" wx:if="{{showQualificationResult}}" bindtap="closeQualificationResult">
  <view class="qualification-modal" catchtap="">
    <view class="modal-header">
      <text class="modal-title">资质预审结果</text>
      <view class="modal-close" bindtap="closeQualificationResult">×</view>
    </view>
    
    <view class="modal-content" wx:if="{{qualificationResult}}">
      <view class="result-item">
        <text class="result-label">贷款资格：</text>
        <text class="result-value {{qualificationResult.canLoan ? 'success' : 'error'}}">
          {{qualificationResult.canLoan ? '符合条件' : '暂不符合'}}
        </text>
      </view>
      
      <view class="result-item" wx:if="{{qualificationResult.canLoan}}">
        <text class="result-label">预估额度：</text>
        <text class="result-value">{{qualificationResult.maxAmount}}万元</text>
      </view>
      
      <view class="result-item" wx:if="{{qualificationResult.canLoan}}">
        <text class="result-label">推荐利率：</text>
        <text class="result-value">{{qualificationResult.recommendedRate}}</text>
      </view>
      
      <view class="suggestions" wx:if="{{qualificationResult.suggestions.length > 0}}">
        <text class="suggestions-title">建议：</text>
        <view class="suggestion-item" wx:for="{{qualificationResult.suggestions}}" wx:key="index">
          <text class="suggestion-text">• {{item}}</text>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <view class="modal-btn" bindtap="closeQualificationResult">我知道了</view>
    </view>
  </view>
</view>

<!-- 提交咨询半屏弹窗 -->
<view class="modal-overlay" wx:if="{{showSubmitModal}}" bindtap="closeSubmitModal" style="align-items: flex-end;">
  <view class="submit-modal" catchtap="">
    <view class="modal-handle"></view>
    <view class="modal-close" bindtap="closeSubmitModal">×</view>

    <view class="submit-content">
      <!-- 提交成功状态 -->
      <view class="success-header">
        <view class="success-icon">✅</view>
        <view class="success-title">提交成功</view>
        <view class="success-subtitle">您的贷款咨询已成功提交</view>
      </view>

      <!-- 咨询编号 -->
      <view class="consultation-id-section">
        <text class="id-label">咨询编号：</text>
        <text class="id-value">{{consultationId}}</text>
        <view class="copy-btn" bindtap="copyConsultationId">复制</view>
      </view>

      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <view class="qrcode-title">扫码添加专属顾问</view>
        <view class="qrcode-container">
          <image class="qrcode-image" src="/image/daikuan.png" mode="aspectFit" bindlongpress="saveQRCode"></image>
          <view class="qrcode-overlay">
            <text class="qrcode-text">长按保存二维码</text>
          </view>
        </view>
      </view>

      <!-- 服务说明 -->
      <view class="service-promise">
        <view class="promise-item">
          <text class="promise-icon">🕐</text>
          <text class="promise-text">24小时内专业顾问联系您</text>
        </view>
        <view class="promise-item">
          <text class="promise-icon">💰</text>
          <text class="promise-text">免费提供最优贷款方案</text>
        </view>
        <view class="promise-item">
          <text class="promise-icon">🎯</text>
          <text class="promise-text">全程协助直到放款成功</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="save-qr-btn" bindtap="saveQRCode">保存二维码</view>
        <view class="close-btn" bindtap="closeSubmitModal">我知道了</view>
      </view>
    </view>
  </view>
</view>
