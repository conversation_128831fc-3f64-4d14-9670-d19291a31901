<!--tools.wxml-->
<view class="container">
  <!-- 水印层 -->
  <view class="watermark-layer">
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
    <view class="watermark-text">深房雷达查成交</view>
  </view>
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/image/searc.png"></image>
      <input
        class="search-input"
        placeholder="搜索二手房成交信息"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-container">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-tags">
        <view
          class="filter-tag {{currentFilter === 'all' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="all"
        >
          <text class="filter-text">全部</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'nanshan' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="nanshan"
        >
          <text class="filter-text">南山</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'baoan' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="baoan"
        >
          <text class="filter-text">宝安</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'futian' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="futian"
        >
          <text class="filter-text">福田</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'luohu' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="luohu"
        >
          <text class="filter-text">罗湖</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'longgang' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="longgang"
        >
          <text class="filter-text">龙岗</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'longhua' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="longhua"
        >
          <text class="filter-text">龙华</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'guangming' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="guangming"
        >
          <text class="filter-text">光明</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'pingshan' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="pingshan"
        >
          <text class="filter-text">坪山</text>
        </view>
        <view
          class="filter-tag {{currentFilter === 'yantian' ? 'active' : ''}}"
          bindtap="onFilterTap"
          data-filter="yantian"
        >
          <text class="filter-text">盐田</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 房源列表 -->
  <view class="property-list">
    <view class="property-item"
      wx:for="{{propertyList}}"
      wx:key="id"
      bindtap="onPropertyTap"
      data-property="{{item}}" >
      <view class="property-main">
        <view class="property-left">
          <view class="property-name">
            <text class="qwq" wx:if="{{item.count>1}}">法拍</text>
 {{item.name}}
          <text class="price-la">{{item.lpp}}</text>
          <text class="price-la">{{item.lao}}</text>
          </view>
          <view class="property-details">
            <text class="detail-item">户型: {{item.layout}}</text>
            <text class="detail-opo">楼层: {{item.floor}}</text>
          </view>
          <view class="property-price">
            <text class="price-label" >成交价:</text>
            <text class="price-value">{{item.price}}万</text>
          </view>
        </view>
        <view class="property-right">
          <view class="pooo" >{{item.date}}</view>
          <view class="property-area">
            <text class="area-label" >面积:{{item.area}}m²</text>
          </view>
          <view class="property-date">
            <text class="area-l" >朝向:{{item.area}}</text>
          </view>
          <view class="property-unit" >
            <text class="unit-label">单价:{{item.unitPrice}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

 
</view>
