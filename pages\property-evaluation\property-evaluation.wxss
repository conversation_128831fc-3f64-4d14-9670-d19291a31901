/* pages/property-evaluation/property-evaluation.wxss */
page {
  height: 100vh;
  overflow: hidden;
}

.container {
  width: 750rpx;
  height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 进度条 */
.progress-container {
  width: 100%;
  background-color: white;
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-sizing: border-box;
  flex-shrink: 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-top: -200rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #e5e5e5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444 0%, #ff6b6b 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  min-width: 80rpx;
  text-align: right;
  flex-shrink: 0;
}

/* 步骤标题 */
.step-header {
  width: 100%;
  background-color: white;
  padding: 40rpx 30rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  flex-shrink: 0;
}

.step-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
  max-width: 690rpx;

}

.step-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
  max-width: 690rpx;
  line-height: 1.4;
}

/* 表单内容 */
.form-content {
  flex: 1;
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

.step-content {
  width: 100%;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  margin-bottom: 30rpx;
}

.form-item {
  width: 100%;
  margin-bottom: 40rpx;
  box-sizing: border-box;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 20rpx;
  max-width: 100%;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
  max-width: 100%;
}

.form-input:focus {
  border-color: #ff4444;
  background-color: white;
}

.input-with-unit {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 100%;
}

.input-with-unit .form-input {
  flex: 1;
  min-width: 0;
}

.input-unit {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  min-width: 60rpx;
}

/* 选择器样式 */
.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  background-color: #fafafa;
  box-sizing: border-box;
}

.picker-display .placeholder {
  color: #999;
  font-size: 30rpx;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  top: -15rpx;
}

.picker-display .selected {
  color: #333;
  font-size: 30rpx;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  top: -15rpx;
}

.arrow {
  color: #999;
  font-size: 32rpx;
  flex-shrink: 0;
  margin-left: 10rpx;
}

/* 楼层输入 */
.floor-input {
  display: flex;
  align-items: center;
  gap: 20rpx;
  width: 100%;
}

.floor-input-item {
  flex: 1;
  height: 88rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #fafafa;
  text-align: center;
  box-sizing: border-box;
  min-width: 0;
}

.floor-separator {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
}

/* 多选容器 */
.multi-select-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.multi-select-item {
  padding: 16rpx 32rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fafafa;
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-width: 120rpx;
  text-align: center;
  flex-shrink: 0;
}

.multi-select-item.selected {
  border-color: #ff4444;
  background-color: #ff4444;
  color: white;
}

/* 图片上传 */
.upload-section {
  width: 100%;
  margin-bottom: 50rpx;
  box-sizing: border-box;
}

.upload-section:last-child {
  margin-bottom: 0;
}

.upload-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 20rpx;
  max-width: 100%;
}

.upload-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.single-upload {
  display: flex;
  width: 100%;
}

.upload-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  z-index: 10;
}

.upload-placeholder {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  box-sizing: border-box;
  flex-shrink: 0;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 750rpx;
  background-color: white;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  z-index: 100;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-width: 0;
}

.btn-primary {
  background: linear-gradient(135deg, #ff4444 0%, #ff6b6b 100%);
  color: white;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
  border: 2rpx solid #e5e5e5;
}

.btn:active {
  opacity: 0.8;
}

/* 750rpx适配优化 */
@media screen and (max-width: 750rpx) {
  .container {
    width: 100vw;
    max-width: 750rpx;
  }

  .progress-container,
  .step-header,
  .form-content {
    width: 100%;
  }

  .bottom-buttons {
    width: 100%;
    left: 0;
    transform: none;
    max-width: 750rpx;
  }
}

/* 确保在所有设备上的高度适配 */
@media screen and (max-height: 600px) {
  .form-content {
    padding: 20rpx;
  }

  .step-content {
    padding: 20rpx;
    margin-bottom: 20rpx;
  }

  .form-item {
    margin-bottom: 30rpx;
  }

  .upload-section {
    margin-bottom: 30rpx;
  }
}

/* 小屏幕适配 */
@media screen and (max-width: 600rpx) {
  .upload-item,
  .upload-placeholder {
    width: 160rpx;
    height: 160rpx;
  }

  .multi-select-container {
    gap: 15rpx;
  }

  .multi-select-item {
    padding: 12rpx 24rpx;
    font-size: 26rpx;
    min-width: 100rpx;
  }

  .floor-input {
    gap: 15rpx;
  }

  .input-with-unit {
    gap: 12rpx;
  }

  .input-unit {
    min-width: 50rpx;
  }
}