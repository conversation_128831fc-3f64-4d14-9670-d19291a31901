// pages/house-consultation/house-consultation.js
Page({
  data: {
    // 弹窗状态管理
    showPurchaseModal: false,
    showPaymentModal: false,
    showQrcodeModal: false,

    // 服务信息
    serviceInfo: {
      title: '行舟深房咨询',
      price: 498,
      originalPrice: 498,
      buyCount: '2200+人买过',
      tags: ['超硬核', '脑有用'],
      serviceType: '房产深度咨询'
    },
    // 销售说明
    salesDescription: [
    ],
    
    // 购买信息
    purchaseInfo: {
      price: 498.00,
      serviceTitle: '房产深度咨询',
      serviceDescription: '长期服务，至买到房为止'
    },
    
    // API配置
    apiConfig: {
      baseUrl: 'https://api.example.com',
      endpoints: {
        createOrder: '/consultation/order/create',
        getServiceDetail: '/consultation/service/detail',
        submitConsultation: '/consultation/submit'
      }
    }
  },

  onLoad: function (options) {
    console.log('买房咨询页面加载', options);
    // 页面加载时初始化数据，暂时不调用API
    this.initPageData();
  },

  // 初始化页面数据
  initPageData: function() {
    // 可以在这里调用API加载数据
    console.log('页面数据初始化完成');
  },

  // 立即购买 - 显示购买弹窗
  onPurchase: function() {
    this.setData({
      showPurchaseModal: true
    });
  },

  // 关闭购买弹窗
  closePurchaseModal: function() {
    this.setData({
      showPurchaseModal: false
    });
  },

  // 确认购买 - 显示支付确认弹窗
  confirmPurchase: function() {
    this.setData({
      showPurchaseModal: false,
      showPaymentModal: true
    });
  },

  // 关闭支付弹窗
  closePaymentModal: function() {
    this.setData({
      showPaymentModal: false
    });
  },

  // 确认支付 - 显示二维码弹窗
  confirmPayment: function() {
    this.setData({
      showPaymentModal: false
    });

    // 模拟支付处理
    wx.showLoading({
      title: '支付处理中...'
    });

    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '支付成功',
        icon: 'success',
        duration: 1500
      });

      // 显示二维码弹窗
      setTimeout(() => {
        this.setData({
          showQrcodeModal: true
        });
      }, 1500);
    }, 2000);
  },

  // 关闭二维码弹窗
  closeQrcodeModal: function() {
    this.setData({
      showQrcodeModal: false
    });
  },

  // 创建订单
  createOrder: function() {
    const { baseUrl, endpoints } = this.data.apiConfig;
    const { purchaseInfo } = this.data;

    // 模拟API调用
    wx.showLoading({
      title: '处理中...'
    });

    // 模拟网络请求
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '购买成功',
        icon: 'success'
      });

      // 可以在这里调用真实的API
      // wx.request({
      //   url: baseUrl + endpoints.createOrder,
      //   method: 'POST',
      //   data: {
      //     serviceType: 'house_consultation',
      //     price: purchaseInfo.price,
      //     serviceTitle: purchaseInfo.serviceTitle,
      //     timestamp: Date.now()
      //   },
      //   success: (res) => {
      //     // 处理成功响应
      //   },
      //   fail: (err) => {
      //     // 处理错误
      //   }
      // });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  // 分享页面
  onShareAppMessage: function () {
    return {
      title: '行舟深房咨询 - 专业买房指导',
      path: '/pages/house-consultation/house-consultation',
      imageUrl: '/image/share-consultation.png'
    };
  }
});
