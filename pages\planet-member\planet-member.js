// pages/planet-member/planet-member.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const util = require('../../utils/util.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 页面加载状态
    loading: false,
    
    // 会员等级体系
    serviceInfo: {
      title: '星球会员',
      price: 498,
      originalPrice: 498,
      buyCount: '2200+人买过',
      tags: ['超硬核', '脑有用'],
      serviceType: '房产深度咨询'
    },
    
    // 会员等级体系
    memberLevels: [
      {
        id: 1,
        name: '星球会员',
        price: 498,
        originalPrice: 199,
        duration: '1个月',
        color: '#CD7F32',
        bgColor: '#FFF8E1',
        privileges: [
          '专属房源推荐',
          '优先看房预约',
          '专业咨询服务',
          '市场分析报告'
        ]
      },
    
    
    ],

    // 选中的会员等级
    selectedLevel: 1, // 默认选择白银会员
    
    // 会员特权详情
    memberBenefits: [
      {
        icon: '🏠',
        title: '专属房源',
        desc: '优质房源优先推送，抢占市场先机'
      },
      {
        icon: '👨‍💼',
        title: '专属顾问',
        desc: '一对一专业顾问，全程贴心服务'
      },
      {
        icon: '📊',
        title: '市场分析',
        desc: '专业市场分析报告，投资决策更精准'
      },
      {
        icon: '💰',
        title: '贷款优惠',
        desc: '合作银行绿色通道，贷款更便捷'
      },
      {
        icon: '⚖️',
        title: '法律保障',
        desc: '专业法律咨询，交易风险全覆盖'
      },
      {
        icon: '🎨',
        title: '装修服务',
        desc: '装修设计咨询，打造理想家居'
      }
    ],
    
   
    // 表单验证状态
    formValid: false,
    
    // 半屏弹窗状态
    showMemberModal: false,
    joinSuccess: false,
    membershipId: '',

    // 支付弹窗状态
    showPaymentModal: false,
    paymentSuccess: false,
    orderId: '',
    
    // 知识星球介绍
    planetIntro: {
      title: '星球会员介绍',
      subtitle: '专业房产投资学习社群',
      features: [
        {
          icon: '📚',
          title: '专业知识库',
          desc: '涵盖房产投资、市场分析、政策解读等专业内容'
        },
        {
          icon: '👥',
          title: '精英社群',
          desc: '汇聚房产投资专家、成功投资者、行业精英'
        },
        {
          icon: '💡',
          title: '实战分享',
          desc: '真实案例分析、投资心得、避坑指南'
        },
        {
          icon: '🎯',
          title: '精准服务',
          desc: '个性化投资建议、专属咨询、优质房源推荐'
        }
      ],
      highlights: [
        '每日市场分析报告',
        '专家在线答疑解惑',
        '独家房源信息分享',
        '投资策略深度解析',
        '政策变化及时解读',
        '成功案例详细剖析'
      ]
    },

    // 购买流程说明
    purchaseSteps: [
      {
        step: 1,
        icon: '🎯',
        title: '选择会员',
        desc: '根据需求选择合适的会员等级'
      },
      {
        step: 2,
        icon: '📝',
        title: '填写信息',
        desc: '完善个人信息，便于提供精准服务'
      },
      {
        step: 3,
        icon: '💳',
        title: '在线支付',
        desc: '支持微信支付、支付宝等多种方式'
      },
      {
        step: 4,
        icon: '🎉',
        title: '开通成功',
        desc: '立即享受专属会员权益和服务'
      }
    ],

   
  
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('星球会员页面加载');
    this.checkFormValid();
  },

  /**
   * 会员等级选择
   */
  onLevelSelect: function(e) {
    const levelId = parseInt(e.currentTarget.dataset.id);
    this.setData({
      selectedLevel: levelId
    });
    console.log('选择会员等级:', levelId);
  },

  /**
   * 表单输入处理
   */
  onNameInput: function(e) {
    this.setData({
      'userForm.name': e.detail.value
    });
    this.checkFormValid();
  },

  onPhoneInput: function(e) {
    const value = e.detail.value.replace(/[^\d]/g, '');
    this.setData({
      'userForm.phone': value
    });
    this.checkFormValid();
  },

  onWechatInput: function(e) {
    this.setData({
      'userForm.wechat': e.detail.value
    });
    this.checkFormValid();
  },

  onRequirementsInput: function(e) {
    this.setData({
      'userForm.requirements': e.detail.value
    });
  },

  /**
   * 表单验证
   */
  checkFormValid: function() {
    const { name, phone, wechat } = this.data.userForm;
    const phoneRegex = /^1[3-9]\d{9}$/;
    
    const isValid = name.trim() !== '' && 
                   phoneRegex.test(phone) && 
                   wechat.trim() !== '';
    
    this.setData({
      formValid: isValid
    });
  },

  /**
   * 提交会员申请
   */
  onSubmitMembership: function() {
    if (!this.data.formValid) {
      util.showError('请完善必填信息');
      return;
    }

    const selectedMember = this.data.memberLevels.find(level => level.id === this.data.selectedLevel);
    
    console.log('提交会员申请:', {
      level: selectedMember,
      userInfo: this.data.userForm
    });

    // 模拟提交处理
    util.showLoading('处理中...');
    
    setTimeout(() => {
      util.hideLoading();
      
      // 生成会员编号
      const membershipId = 'PM' + Date.now();
      
      // 显示成功弹窗
      this.setData({
        showMemberModal: true,
        joinSuccess: true,
        membershipId: membershipId
      });
    }, 2000);
  },

  /**
   * 关闭会员弹窗
   */
  closeMemberModal: function() {
    this.setData({
      showMemberModal: false,
      joinSuccess: false,
      membershipId: ''
    });
  },

  /**
   * 复制会员编号
   */
  copyMembershipId: function() {
    wx.setClipboardData({
      data: this.data.membershipId,
      success: () => {
        util.showSuccess('会员编号已复制');
      }
    });
  },

  /**
   * 保存二维码
   */
  saveQRCode: function() {
    wx.showModal({
      title: '保存二维码',
      content: '长按二维码图片可以保存到相册',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 立即购买
   */
  onPurchaseNow: function() {
    const selectedMember = this.data.memberLevels.find(level => level.id === this.data.selectedLevel);

    console.log('立即购买:', {
      level: selectedMember
    });

    // 直接显示支付弹窗
    this.setData({
      showPaymentModal: true
    });
  },

  /**
   * 关闭支付弹窗
   */
  closePaymentModal: function() {
    this.setData({
      showPaymentModal: false,
      paymentSuccess: false,
      orderId: ''
    });
  },

  /**
   * 确认支付
   */
  confirmPayment: function() {
    const selectedMember = this.data.memberLevels.find(level => level.id === this.data.selectedLevel);

    util.showLoading('支付中...');

    // 模拟支付处理
    setTimeout(() => {
      util.hideLoading();

      // 生成订单编号
      const orderId = 'ORD' + Date.now();

      // 显示支付成功状态
      this.setData({
        paymentSuccess: true,
        orderId: orderId
      });

      console.log('支付成功:', orderId);
    }, 2000);
  },

  /**
   * 保存支付二维码
   */
  savePaymentQRCode: function() {
    wx.showModal({
      title: '保存二维码',
      content: '长按二维码图片可以保存到相册',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 复制订单编号
   */
  copyOrderId: function() {
    wx.setClipboardData({
      data: this.data.orderId,
      success: () => {
        util.showSuccess('订单编号已复制');
      }
    });
  },

  /**
   * FAQ展开/收起
   */
  toggleFAQ: function(e) {
    const index = e.currentTarget.dataset.index;
    const faqList = this.data.faqList;
    faqList[index].expanded = !faqList[index].expanded;
    this.setData({
      faqList: faqList
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 分享
   */
  onShareAppMessage: function() {
    return {
      title: '深房雷达 - 星球会员',
      path: '/pages/planet-member/planet-member',
      imageUrl: '/image/xingzhouzixun.png'
    };
  }
});
