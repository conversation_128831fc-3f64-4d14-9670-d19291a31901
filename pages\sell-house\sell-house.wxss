/* pages/sell-house/sell-house.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 表单容器 */
.form-container {
  padding: 30rpx;
  width: 750rpx;
  margin-top: -220rpx;
}

/* 表单区块 */
.form-section {
  background-color: white;
  overflow: hidden;
  width: 750rpx;
}

.section-title {
  background-color: #FFFFFF;
  padding: 40rpx;
  font-size: 32rpx;
  color: #333;
}

/* 表单项 */
.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  min-height: 80rpx;
  border-bottom: 1rpx solid rgb(204, 199, 199);
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  min-width: 160rpx;
}

/* 输入框容器 */
.form-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.form-input {
  flex: 1;
  text-align: right;
  font-size: 30rpx;
  color: #333;
  padding: 0 20rpx;
}

.form-unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 选择器值容器 */
.form-value-container {
  display: flex;
  align-items: center;
}

.form-value {
  font-size: 30rpx;
  color: #333;
  margin-right: 10rpx;
}

.arrow-text {
  font-size: 28rpx;
  color: #999;
}

/* 文本域项 */
.textarea-item {
  flex-direction: column;
  align-items: stretch;
  padding: 30rpx;
}

.form-textarea {
  width: 100%;
  min-height: 100rpx;
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  padding: 20rpx;
  border-radius: 10rpx;
  background-color: #FFFFFF
}

/* 提交记录链接 */
.submit-record {
  text-align: center;
  padding: 30rpx;
}

.record-link {
  color: #FF4444;
  font-size: 28rpx;
  text-decoration: underline;
}

/* 提交按钮容器 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
}

.submit-btn {
  width: 750rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
}

.submit-btn:disabled {
  background: #ccc;
  box-shadow: none;
}

.submit-btn:not(:disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.3);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .form-label {
    min-width: 140rpx;
    font-size: 30rpx;
  }
  
  .form-input {
    font-size: 28rpx;
  }
  
  .form-value {
    font-size: 28rpx;
  }
}

/* 输入框聚焦状态 */
.form-input:focus {
  color: #FF4444;
}

/* 选择器项目悬停效果 */
.form-item:active {
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}
