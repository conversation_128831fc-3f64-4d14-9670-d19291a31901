// pages/property-evaluation/result.js
Page({
  data: {
    formData: {},
    evaluation: {
      minPrice: 0,
      maxPrice: 0,
      avgPrice: 0,
      pricePerSqm: 0,
      factors: [],
      totalScore: 0
    },
    loading: false
  },

  onLoad(options) {
    console.log('评估结果页面加载', options);

    if (options.data) {
      try {
        const formData = JSON.parse(decodeURIComponent(options.data));
        this.setData({ formData });
        // 直接计算评估结果，不显示加载状态
        this.calculateEvaluation(formData);
      } catch (e) {
        console.error('解析表单数据失败:', e);
        wx.showToast({
          title: '数据解析失败',
          icon: 'none'
        });
      }
    } else {
      wx.showToast({
        title: '缺少评估数据',
        icon: 'none'
      });
    }
  },

  // 计算房产评估
  calculateEvaluation: function(formData) {
    console.log('开始计算评估:', formData);

    // 基础单价（深圳平均房价，可根据实际情况调整）
    let basePrice = 60000; // 每平方米基础价格

    // 各因素评分和权重
    const factors = [];
    let totalScore = 100; // 基础分数

    // 1. 户型评分 (权重: 15%)
    const houseTypeScore = this.getHouseTypeScore(formData.houseType);
    factors.push({
      name: '户型',
      score: houseTypeScore,
      weight: 15,
      impact: ((houseTypeScore - 50) / 50 * 15).toFixed(1)
    });

    // 2. 房龄评分 (权重: 20%)
    const ageScore = this.getAgeScore(formData.houseAge);
    factors.push({
      name: '房龄',
      score: ageScore,
      weight: 20,
      impact: ((ageScore - 50) / 50 * 20).toFixed(1)
    });

    // 3. 装修评分 (权重: 10%)
    const decorationScore = this.getDecorationScore(formData.decoration);
    factors.push({
      name: '装修',
      score: decorationScore,
      weight: 10,
      impact: ((decorationScore - 50) / 50 * 10).toFixed(1)
    });

    // 4. 楼层评分 (权重: 15%)
    const floorScore = this.getFloorScore(formData.currentFloor, formData.totalFloor);
    factors.push({
      name: '楼层',
      score: floorScore,
      weight: 15,
      impact: ((floorScore - 50) / 50 * 15).toFixed(1)
    });

    // 5. 朝向评分 (权重: 10%)
    const orientationScore = this.getOrientationScore(formData.orientation);
    factors.push({
      name: '朝向',
      score: orientationScore,
      weight: 10,
      impact: ((orientationScore - 50) / 50 * 10).toFixed(1)
    });

    // 6. 配套设施评分 (权重: 20%)
    const facilitiesScore = this.getFacilitiesScore(formData.facilities);
    factors.push({
      name: '配套设施',
      score: facilitiesScore,
      weight: 20,
      impact: ((facilitiesScore - 50) / 50 * 20).toFixed(1)
    });

    // 7. 特殊情况扣分 (权重: 10%)
    const specialScore = this.getSpecialConditionsScore(formData.specialConditions);
    factors.push({
      name: '特殊情况',
      score: specialScore,
      weight: 10,
      impact: ((specialScore - 50) / 50 * 10).toFixed(1)
    });

    // 计算总分
    totalScore = factors.reduce((sum, factor) => {
      return sum + (factor.score * factor.weight / 100);
    }, 0);

    // 根据总分调整价格
    const priceMultiplier = totalScore / 100;
    const adjustedPrice = basePrice * priceMultiplier;

    // 计算价格区间 (±8%)
    const minPrice = adjustedPrice * 0.92;
    const maxPrice = adjustedPrice * 1.08;

    // 计算总价
    const area = parseFloat(formData.buildingArea) || 100;
    const minTotalPrice = (minPrice * area / 10000).toFixed(0);
    const maxTotalPrice = (maxPrice * area / 10000).toFixed(0);
    const avgTotalPrice = (adjustedPrice * area / 10000).toFixed(0);

    this.setData({
      evaluation: {
        minPrice: minTotalPrice,
        maxPrice: maxTotalPrice,
        avgPrice: avgTotalPrice,
        pricePerSqm: adjustedPrice.toFixed(0),
        factors: factors,
        totalScore: totalScore.toFixed(1)
      }
    });
  },

  // 户型评分
  getHouseTypeScore: function(houseType) {
    const scores = {
      '1室1厅': 40,
      '2室1厅': 60,
      '2室2厅': 70,
      '3室1厅': 65,
      '3室2厅': 80,
      '4室2厅': 75,
      '4室3厅': 85,
      '5室及以上': 70
    };
    return scores[houseType] || 50;
  },

  // 房龄评分
  getAgeScore: function(houseAge) {
    const scores = {
      '1年内': 95,
      '1-3年': 85,
      '3-5年': 75,
      '5-10年': 65,
      '10-15年': 45,
      '15年以上': 30
    };
    return scores[houseAge] || 50;
  },

  // 装修评分
  getDecorationScore: function(decoration) {
    const scores = {
      '毛坯': 30,
      '简装': 50,
      '精装': 80,
      '豪装': 95
    };
    return scores[decoration] || 50;
  },

  // 楼层评分
  getFloorScore: function(currentFloor, totalFloor) {
    const current = parseInt(currentFloor) || 1;
    const total = parseInt(totalFloor) || 1;
    const ratio = current / total;

    if (ratio <= 0.2) return 40; // 低楼层
    if (ratio <= 0.4) return 70; // 中低楼层
    if (ratio <= 0.8) return 85; // 中高楼层
    return 60; // 高楼层
  },

  // 朝向评分
  getOrientationScore: function(orientation) {
    const scores = {
      '南北通透': 95,
      '南向': 85,
      '东南': 80,
      '西南': 75,
      '东向': 65,
      '西向': 55,
      '北向': 45,
      '东北': 50,
      '西北': 45
    };
    return scores[orientation] || 50;
  },

  // 配套设施评分
  getFacilitiesScore: function(facilities) {
    if (!facilities || !Array.isArray(facilities)) return 30;

    const facilityScores = {
      'subway': 25,
      'school': 20,
      'hospital': 15,
      'mall': 15,
      'park': 10,
      'bus': 10
    };

    let score = 30; // 基础分
    facilities.forEach(facility => {
      score += facilityScores[facility] || 0;
    });

    return Math.min(score, 95); // 最高95分
  },

  // 特殊情况评分
  getSpecialConditionsScore: function(conditions) {
    if (!conditions || !Array.isArray(conditions)) return 70;

    const penalties = {
      'topFloor': -15,
      'bottomFloor': -10,
      'streetFacing': -10,
      'poorLight': -20,
      'noElevator': -15,
      'noisePollution': -15
    };

    let score = 70; // 基础分
    conditions.forEach(condition => {
      score += penalties[condition] || 0;
    });

    return Math.max(score, 10); // 最低10分
  },

  // 重新评估
  reEvaluate: function() {
    wx.navigateBack();
  },

  // 分享结果
  shareResult: function() {
    const { avgPrice, pricePerSqm } = this.data.evaluation;
    const { communityName, buildingArea } = this.data.formData;

    return {
      title: `${communityName}房产评估结果：${avgPrice}万元`,
      desc: `建筑面积${buildingArea}㎡，单价${pricePerSqm}元/㎡`,
      path: '/pages/property-evaluation/property-evaluation'
    };
  },

  onShareAppMessage: function() {
    return this.shareResult();
  }
})