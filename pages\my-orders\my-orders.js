// pages/my-orders/my-orders.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 页面加载状态
    loading: true,
    refreshing: false,

    // 订单状态筛选
    orderTabs: [
      { id: 'all', name: '全部', count: 0 },
      { id: 'pending', name: '待支付', count: 0 },
      { id: 'paid', name: '已支付', count: 0 },
      { id: 'completed', name: '已完成', count: 0 },
      { id: 'cancelled', name: '已取消', count: 0 }
    ],
    currentTab: 'all',

    // 订单列表数据
    orderList: [
      {
        id: 'ORD202401001',
        type: 'consultation',
        title: '房产投资咨询服务',
        description: '专业房产投资顾问一对一咨询',
        amount: 498,
        status: 'pending',
        statusText: '待支付',
        statusColor: '#FF6B35',
        createTime: '2024-01-15 14:30:00',
        expireTime: '2024-01-15 16:30:00',
        image: '/image/xingzhouzixun.png',
        actions: ['cancel', 'pay']
      },
      {
        id: 'ORD202401002',
        type: 'membership',
        title: '深房雷达星球会员',
        description: '白银会员 - 3个月服务',
        amount: 299,
        status: 'paid',
        statusText: '已支付',
        statusColor: '#4CAF50',
        createTime: '2024-01-14 10:20:00',
        payTime: '2024-01-14 10:25:00',
        image: '/image/xingzhouzixun.png',
        actions: ['view']
      },
      {
        id: 'ORD202401003',
        type: 'evaluation',
        title: '房产价值评估报告',
        description: '深圳南山区某小区房产评估',
        amount: 199,
        status: 'completed',
        statusText: '已完成',
        statusColor: '#666',
        createTime: '2024-01-10 09:15:00',
        payTime: '2024-01-10 09:20:00',
        completeTime: '2024-01-12 16:00:00',
        image: '/image/xingzhouzixun.png',
        actions: ['view', 'download']
      },
      {
        id: 'ORD202401004',
        type: 'consultation',
        title: '贷款咨询服务',
        description: '购房贷款方案咨询',
        amount: 298,
        status: 'cancelled',
        statusText: '已取消',
        statusColor: '#999',
        createTime: '2024-01-08 15:45:00',
        cancelTime: '2024-01-08 16:00:00',
        image: '/image/xingzhouzixun.png',
        actions: ['reorder']
      }
    ],

    // 筛选后的订单列表
    filteredOrderList: [],

    // 分页相关
    currentPage: 1,
    pageSize: 10,
    hasMore: true,

    // 空状态
    showEmpty: false,
    emptyText: '暂无订单',

    // API接口配置
    apiEndpoints: {
      getOrderList: '/api/orders/list',
      getOrderDetail: '/api/orders/detail',
      cancelOrder: '/api/orders/cancel',
      payOrder: '/api/orders/pay',
      confirmOrder: '/api/orders/confirm'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('我的订单页面加载');
    this.loadOrderList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时刷新数据
    if (!this.data.loading) {
      this.refreshOrderList();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    console.log('下拉刷新订单列表');
    this.setData({
      refreshing: true
    });
    this.refreshOrderList();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    console.log('上拉加载更多订单');
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreOrders();
    }
  },

  /**
   * 订单状态筛选
   */
  onTabChange: function(e) {
    const tabId = e.currentTarget.dataset.id;
    console.log('切换订单状态:', tabId);

    this.setData({
      currentTab: tabId
    });

    this.filterOrderList(tabId);

    // 滚动到顶部，确保筛选后的订单显示在第一位
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  /**
   * 订单操作处理
   */
  onOrderAction: function(e) {
    const { action, orderId } = e.currentTarget.dataset;
    console.log('订单操作:', action, orderId);

    switch(action) {
      case 'pay':
        this.payOrder(orderId);
        break;
      case 'cancel':
        this.cancelOrder(orderId);
        break;
      case 'view':
        this.viewOrderDetail(orderId);
        break;
      case 'download':
        this.downloadReport(orderId);
        break;
      case 'reorder':
        this.reorderService(orderId);
        break;
      default:
        console.log('未知操作:', action);
    }
  },

  /**
   * 订单详情查看
   */
  onOrderTap: function(e) {
    const orderId = e.currentTarget.dataset.id;
    this.viewOrderDetail(orderId);
  },

  /**
   * 加载订单列表
   */
  loadOrderList: function() {
    console.log('加载订单列表');

    this.setData({
      loading: true
    });

    // 模拟API调用
    setTimeout(() => {
      this.updateOrderCounts();
      this.filterOrderList(this.data.currentTab);

      this.setData({
        loading: false,
        refreshing: false
      });

      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 刷新订单列表
   */
  refreshOrderList: function() {
    console.log('刷新订单列表');

    this.setData({
      currentPage: 1,
      hasMore: true
    });

    this.loadOrderList();
  },

  /**
   * 加载更多订单
   */
  loadMoreOrders: function() {
    console.log('加载更多订单');

    if (!this.data.hasMore) {
      return;
    }

    this.setData({
      loading: true
    });

    // 模拟API调用
    setTimeout(() => {
      const newPage = this.data.currentPage + 1;

      // 模拟没有更多数据
      if (newPage > 3) {
        this.setData({
          hasMore: false,
          loading: false
        });

        wx.showToast({
          title: '没有更多订单了',
          icon: 'none'
        });
        return;
      }

      this.setData({
        currentPage: newPage,
        loading: false
      });
    }, 1000);
  },

  /**
   * 更新订单数量统计
   */
  updateOrderCounts: function() {
    const orderTabs = this.data.orderTabs;
    const orderList = this.data.orderList;

    orderTabs[0].count = orderList.length; // 全部
    orderTabs[1].count = orderList.filter(order => order.status === 'pending').length; // 待支付
    orderTabs[2].count = orderList.filter(order => order.status === 'paid').length; // 已支付
    orderTabs[3].count = orderList.filter(order => order.status === 'completed').length; // 已完成
    orderTabs[4].count = orderList.filter(order => order.status === 'cancelled').length; // 已取消

    this.setData({
      orderTabs: orderTabs
    });
  },

  /**
   * 筛选订单列表
   */
  filterOrderList: function(status) {
    let filteredList = this.data.orderList;

    if (status !== 'all') {
      filteredList = this.data.orderList.filter(order => order.status === status);
    }

    this.setData({
      filteredOrderList: filteredList,
      showEmpty: filteredList.length === 0
    });
  },

  /**
   * 支付订单
   */
  payOrder: function(orderId) {
    console.log('支付订单:', orderId);

    wx.showModal({
      title: '确认支付',
      content: '确定要支付这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '支付中...' });

          // 模拟支付API调用
          setTimeout(() => {
            wx.hideLoading();

            // 更新订单状态
            this.updateOrderStatus(orderId, 'paid', '已支付');

            wx.showToast({ title: '支付成功', icon: 'success' });
          }, 2000);
        }
      }
    });
  },

  /**
   * 取消订单
   */
  cancelOrder: function(orderId) {
    console.log('取消订单:', orderId);

    wx.showModal({
      title: '取消订单',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '处理中...' });

          // 模拟取消API调用
          setTimeout(() => {
            wx.hideLoading();

            // 更新订单状态
            this.updateOrderStatus(orderId, 'cancelled', '已取消');

            wx.showToast({ title: '订单已取消', icon: 'success' });
          }, 1000);
        }
      }
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function(orderId) {
    console.log('查看订单详情:', orderId);

    wx.showToast({
      title: '订单详情功能',
      icon: 'none'
    });
  },

  /**
   * 下载报告
   */
  downloadReport: function(orderId) {
    console.log('下载报告:', orderId);

    wx.showToast({
      title: '报告下载功能',
      icon: 'none'
    });
  },

  /**
   * 重新下单
   */
  reorderService: function(orderId) {
    console.log('重新下单:', orderId);

    const order = this.data.orderList.find(item => item.id === orderId);
    if (order) {
      // 根据订单类型跳转到对应页面
      switch(order.type) {
        case 'consultation':
          wx.navigateTo({
            url: '/pages/house-consultation/house-consultation'
          });
          break;
        case 'membership':
          wx.navigateTo({
            url: '/pages/planet-member/planet-member'
          });
          break;
        case 'evaluation':
          wx.navigateTo({
            url: '/pages/property-evaluation/property-evaluation'
          });
          break;
        default:
          wx.showToast({
            title: '服务类型未知',
            icon: 'none'
          });
      }
    }
  },

  /**
   * 更新订单状态
   */
  updateOrderStatus: function(orderId, newStatus, newStatusText) {
    const orderList = this.data.orderList;
    const orderIndex = orderList.findIndex(order => order.id === orderId);

    if (orderIndex !== -1) {
      orderList[orderIndex].status = newStatus;
      orderList[orderIndex].statusText = newStatusText;

      // 更新状态颜色
      const statusColors = {
        'pending': '#FF6B35',
        'paid': '#4CAF50',
        'completed': '#666',
        'cancelled': '#999'
      };
      orderList[orderIndex].statusColor = statusColors[newStatus];

      this.setData({
        orderList: orderList
      });

      // 重新筛选和统计
      this.updateOrderCounts();
      this.filterOrderList(this.data.currentTab);
    }
  },

  /**
   * 去下单（空状态按钮）
   */
  goShopping: function() {
    console.log('去下单');

    // 跳转到首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function () {
    return {
      title: '深房雷达 - 我的订单',
      path: '/pages/my-orders/my-orders',
      imageUrl: '/image/wddd.png'
    };
  }
})