/* pages/property-evaluation/result.wxss */
.container {
  width: 750rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  margin: 0 auto;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff4444;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* 结果容器 */
.result-container {
  width: 750rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 房屋信息卡片 */
.house-info-card {
  width: 100%;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  max-width: 690rpx;
  margin-top: -210rpx;
}

.house-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-details {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
}

.detail-item {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.detail-separator {
  margin: 0 15rpx;
  color: #ccc;
  flex-shrink: 0;
}

/* 估值卡片 */
.valuation-card {
  background: linear-gradient(135deg, #ff4444 0%, #ff6b6b 100%);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(255, 68, 68, 0.3);
}

.valuation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.valuation-title {
  font-size: 32rpx;
  font-weight: 600;
}

.score-badge {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
}

.score-text {
  font-size: 24rpx;
  font-weight: 500;
}

.price-display {
  text-align: center;
}

.price-range {
  margin-bottom: 20rpx;
}

.price-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.price-unit {
  font-size: 28rpx;
  opacity: 0.9;
}

.avg-price {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 15rpx;
}

.avg-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.avg-number {
  font-size: 32rpx;
  font-weight: 600;
}

.unit-price {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15rpx;
}

.unit-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.unit-number {
  font-size: 28rpx;
  font-weight: 500;
}

/* 评估因素卡片 */
.factors-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.factors-header {
  margin-bottom: 30rpx;
}

.factors-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}



.factor-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.factor-item:last-child {
  border-bottom: none;
}

.factor-info {
  flex: 1;
  min-width: 0;
}

.factor-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}

.factor-weight {
  font-size: 24rpx;
  color: #999;
}

.factor-score {
  flex: 1;
  margin: 0 30rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.score-bar {
  flex: 1;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444 0%, #ff6b6b 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.score-value {
  font-size: 24rpx;
  color: #666;
  min-width: 60rpx;
}

.factor-impact {
  min-width: 80rpx;
  text-align: right;
  font-size: 24rpx;
  font-weight: 500;
}

.factor-impact.positive {
  color: #52c41a;
}

.factor-impact.negative {
  color: #ff4d4f;
}

/* 免责声明卡片 */
.disclaimer-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.disclaimer-header {
  margin-bottom: 20rpx;
}

.disclaimer-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}


.disclaimer-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.primary-btn {
  background: linear-gradient(135deg, #ff4444 0%, #ff6b6b 100%);
  color: white;
}

.secondary-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 2rpx solid #e5e5e5;
}

.action-btn:active {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .price-number {
    font-size: 40rpx;
  }

  .factor-score {
    margin: 0 20rpx;
  }

  .factor-impact {
    min-width: 60rpx;
  }
}