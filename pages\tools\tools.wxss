/**tools.wxss**/
.container {
  width: 750rpx;
  min-height: 100vh;
  position: relative;
}
/* 水印层 */
.watermark-layer {
  position: fixed;
  top: 0;
  left: 0rpx;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  pointer-events: none;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(6, 1fr);
  gap: 60rpx 40rpx;
  padding: 80rpx 30rpx;
  transform: rotate(-20deg);
  opacity: 1;
}

.watermark-text {
  font-size: 24rpx;
  color: #ccc;
  font-weight: 300;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  opacity: 0.8;
}



/* 顶部状态栏 */
.status-bar {
  width: 750rpx;
  height: 120rpx;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 2;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.icon-dots {
  display: flex;
  gap: 6rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #999;
  border-radius: 50%;
}

.icon-circle {
  width: 40rpx;
  height: 40rpx;
  background-color: #333;
  border-radius: 50%;
}

/* 搜索栏 */
.search-container {
  width: 750rpx;
  padding: 20rpx 30rpx;
  position: relative;
  z-index: 2;
  margin-top: -200rpx;
}

.search-box {
  width: 685rpx;
  height: 80rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  border: 2rpx solid #333;
}

.search-icon {
  width: 35rpx;
  height: 35rpx;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 筛选标签 */
.filter-container {
  width: 750rpx;
  position: relative;
  z-index: 2;
}

.filter-scroll {
  width: 100%;
  white-space: nowrap;

}

.filter-tags {
  display: flex;
  padding: 0 30rpx;
  gap: 20rpx;
  padding-left: 20rpx;
  
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40rpx;
  padding: 0 24rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  white-space: nowrap;
  flex-shrink: 0;
  border: 1rpx solid #666;
  width: 20rpx;
  margin-left:-15rpx ;
  margin: 0 -5rpx;
}

.filter-tag.active {
  background-color: #FF4444;
}

.filter-text {
  font-size: 28rpx;
  color: #666;
}

.filter-tag.active .filter-text {
  color: white;
}

/* 房源列表 */
.property-list {
  width: 750rpx;
  position: relative;
  z-index: 2;
}

.property-item {
  width: 100%;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  position: relative;
  height: 140rpx;
}

.property-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.property-left {
  flex: 1;
  margin-right: 20rpx;
}

.property-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}
.qwq{
  background-color: #ccc;
  color: white;
  height: 40rpx;
  width: 40rpx;

}
.price-la{
font-size: 26rpx;
color: #666;
margin-left: 10rpx;
}

.property-details {
  display: flex;
  gap: 20rpx;
  margin-bottom: 12rpx;
}

.detail-item {
  font-size: 26rpx;
  color: #666;
  position: absolute;
  top: 75rpx;
}
.detail-opo {
  font-size: 26rpx;
  color: #666;
  position: absolute;
  top: 110rpx;
}
.property-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.price-label {
  font-size: 26rpx;
  color: #666;
  position: absolute;
  top: 150rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF4444;
  position: absolute;
  top: 145rpx;
  left:120rpx;
}

.property-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.property-date {
  color: #999;
  white-space: nowrap;
  font-size: 26rpx;
  color: #666;
}
.pooo{
 color: #999;
  white-space: nowrap;
  font-size: 30rpx;
  position: absolute;
  left: 540rpx;
}
.area-l{
  position: absolute;
  left: 380rpx;
  top: 110rpx;
}
.property-unit {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
}

.area-label{
  position: absolute;
  left: 380rpx;
  white-space: nowrap;
  top: 75rpx;
  font-size: 26rpx;
  color: #666;
}
.unit-label {
  font-size: 26rpx;
  color: #666;
  position: absolute;
  left: 380rpx;
  top: 150rpx;
}

.area-value,
.unit-value {
  font-size: 26rpx;
  color: #333;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: 120rpx;
  background-color: white;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  z-index: 10;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.nav-icon {
  width: 48rpx;
  height: 48rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #666;
}

/* 点击效果 */
.property-item:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
  transition: all 0.1s ease;
}

.filter-tag:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}

.nav-item:active {
  background-color: #f8f8f8;
  transition: all 0.1s ease;
}
