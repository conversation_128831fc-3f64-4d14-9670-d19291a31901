<!--pages/featured-properties/featured-properties.wxml-->
<view class="container">

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <image class="search-icon" src="/image/searc.png" mode="aspectFit"></image>
      <input class="search-input"
             placeholder="{{searchPlaceholder}}"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
      <!-- 清空搜索按钮 -->
      <view class="clear-search-btn"
            wx:if="{{searchKeyword}}"
            bindtap="clearSearch">
        <text class="clear-icon">×</text>
      </view>
    </view>
  </view>

  <!-- 筛选条件栏 -->
  <view class="filter-section">
    <!-- 价格筛选 -->
    <view class="filter-row">
      <view class="filter-item {{selectedPriceIndex === index ? 'active' : ''}}"
            wx:for="{{filterOptions.priceRange}}"
            wx:key="index"
            bindtap="onFilterTap"
            data-type="price"
            data-index="{{index}}">
        <text class="filter-text">{{item}}</text>
      </view>
    </view>

    <!-- 标签筛选 -->
    <view class="filter-row">
      <view class="filter-tag {{selectedTagIndex === index ? 'active' : ''}}"
            wx:for="{{filterOptions.tags}}"
            wx:key="index"
            bindtap="onFilterTap"
            data-type="tag"
            data-index="{{index}}">
        <text class="tag-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索结果提示 -->
  <view class="search-result-tip" wx:if="{{isSearching && searchKeyword}}">
    <text class="tip-text">搜索"{{searchKeyword}}"的结果</text>
    <view class="clear-search-link" bindtap="clearSearch">
      <text class="link-text">清空搜索</text>
    </view>
  </view>

  <!-- 房源列表 -->
  <view class="property-list">
    <view class="property-card"
          wx:for="{{propertyList}}"
          wx:key="id"
          bindtap="onPropertyTap"
          data-id="{{item.id}}">

      <!-- 房源图片容器 -->
     

      <!-- 房源信息 -->
      <view class="property-info">
        <image class="property-image" src="{{item.image}}" mode="aspectFill"></image>
 
        <!-- 标题行 -->
        <view class="title-row">
          <text class="property-title">{{item.title}} {{item.subtitle}}</text>
          <view class="time-tag">{{item.timeAgo}}</view>
        </view>

        <!-- 房源详情 -->
        <view class="property-details">
          <text class="detail-item">{{item.area}}</text>
          <text class="detail-separator">|</text>
          <text class="detail-item">{{item.direction}}</text>
          <text class="detail-separator">|</text>
          <text class="detail-item">{{item.floor}}</text>
          <text class="detail-separator">|</text>
          <text class="detail-item">{{item.location}}</text>
        </view>

        <!-- 价格信息 -->
        <view class="price-section">
          <view class="price-main">
            <text class="total-price">{{item.totalPrice}}</text>
            <text class="unit-price">{{item.unitPrice}}</text>
          </view>
        </view>

        <!-- 房源标签 -->
        <view class="property-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <view class="property-tag" wx:for="{{item.tags}}" wx:key="index">{{item}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据提示 -->
  <view class="no-more-tip" wx:if="{{!hasMore && !loading}}">
    <text class="tip-text">没有更多房源了</text>
  </view>

</view>