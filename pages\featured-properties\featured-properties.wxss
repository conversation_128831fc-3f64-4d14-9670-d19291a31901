/* pages/featured-properties/featured-properties.wxss */

.container {
  background: #f5f5f5;
  padding-bottom: 40rpx;
  margin-top: -200rpx;
}

/* 搜索栏样式 */
.search-section {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 750rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 70rpx;
  position: relative;
  border-width: 2rpx;
  border-style: solid;
}

.search-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 清空搜索按钮 */
.clear-search-btn {
  position: absolute;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 搜索结果提示 */
.search-result-tip {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #E3F2FD;
  padding: 15rpx 20rpx;
  margin: 0 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #1976D2;
}

.clear-search-link {
  padding: 8rpx 16rpx;
  background: #1976D2;
  border-radius: 15rpx;
}

.link-text {
  color: white;
  font-size: 24rpx;
}

/* 筛选条件样式 */
.filter-section {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 750rpx;
  margin-left: 30rpx;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  margin-right: 40rpx;
  padding: 10rpx 0;
  position: relative;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
}

.filter-item.active .filter-text {
  color: #FF4444;
  font-weight: bold;
}

.filter-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: #FF4444;
  border-radius: 2rpx;
}

.filter-tag {
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
}

.filter-tag.active {
  background: #FFE5E5;
  border: 1rpx solid #FF6B6B;
}

.tag-text {
  font-size: 24rpx;
  color: #666;
}

.filter-tag.active .tag-text {
  color: #FF4444;
}

/* 房源列表样式 */
.property-list {
  padding: 0 20rpx;
  width: 750rpx;

}

.property-card {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 房源图片容器 */


.property-image {
  position: relative;
  top: 50rpx;
  background-color: black;
  width: 180rpx;
  height: 140rpx;
  margin-top: -30rpx;
}

.video-play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 4rpx;
}

.property-badge {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  background: #ff3300;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
}

.badge-text {
  font-size: 22rpx;
  font-weight: bold;
}

/* 房源信息样式 */
.property-info {
  padding: 20rpx;
  position: relative;
  width: 750rpx;
  height: 180rpx;
}

.value-tag {
  display: flex;
  align-items: center;
  background: #FFF3E0;
  border: 1rpx solid #FFB74D;
  border-radius: 15rpx;
  padding: 8rpx 12rpx;
  margin-bottom: 15rpx;
}

.value-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.value-text {
  font-size: 22rpx;
  color: #F57C00;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
  position: relative;
}

.property-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  position: relative;
  top: -90rpx;
  left: 190rpx;
}

.time-tag {
  background: #f0f0f0;
  color: #999;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  position: relative;
  left: -40rpx;
  top: 20rpx;
}

.property-details {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  flex-wrap: wrap;
  position: relative;
  top: -90rpx;
  left: 190rpx;
}

.detail-item {
  font-size: 24rpx;
  color: #666;
}

.detail-separator {
  font-size: 24rpx;
  color: #ccc;
  margin: 0 8rpx;
}

.price-section {
  margin-bottom: 15rpx;
  position: relative;
  top: -90rpx;
  left: 190rpx;
}

.price-main {
  display: flex;
  align-items: baseline;
  gap: 15rpx;
  margin-bottom: 8rpx;
}

.total-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF4444;
}

.unit-price {
  font-size: 24rpx;
  color: #999;
}

.price-down {
  font-size: 22rpx;
  color: #FF6B35;
  background: #FFF3E0;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  display: inline-block;
}

.property-tags {
  display: flex;
  gap: 10rpx;
  margin-bottom: 15rpx;
  flex-wrap: wrap;
}

.property-tag {
  background: #E3F2FD;
  color: #1976D2;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
}

.buy-reasons {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 15rpx;
}

.reasons-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.lightning-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.reasons-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #FF6B35;
}

.reason-item {
  display: flex;
  margin-bottom: 8rpx;
}

.reason-number {
  font-size: 22rpx;
  color: #FF6B35;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.reason-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
}

/* 加载状态样式 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4169E1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.no-more-tip {
  text-align: center;
  padding: 40rpx;
}

.no-more-tip .tip-text {
  font-size: 26rpx;
  color: #999;
}