/* pages/my-orders/my-orders.wxss */

.container {
  min-height: 100vh;
  background: white;
  width: 750rpx;
  margin: 0 auto;
  padding-bottom: 40rpx;
}

/* 订单状态筛选栏 */
.order-tabs {
  display: flex;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 0 20rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 750rpx;
  margin-top: -210rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25rpx 0;
  position: relative;
}

.tab-item.active {
  color: #FF4444;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #FF4444;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

.tab-count {
  font-size: 24rpx;
  margin-left: 8rpx;
  opacity: 0.8;
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
  width: 750rpx;
}

.order-card {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #f0f0f0;
}

/* 订单头部 */
.order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 25rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-id {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 22rpx;
  color: #999;
}

.order-status {
  font-size: 26rpx;
  font-weight: 500;
}

/* 订单内容 */
.order-content {
  display: flex;
  padding: 25rpx;
}

.order-image-container {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.order-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.order-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.order-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.order-description {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.order-amount {
  display: flex;
  align-items: baseline;
}

.amount-symbol {
  font-size: 24rpx;
  color: #FF4444;
  margin-right: 4rpx;
}

.amount-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF4444;
}

/* 订单操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15rpx;
  padding: 20rpx 25rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: -20rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 20rpx;
  background: white;
  height: 20rpx;
}

.action-btn.primary {
  background: #FF4444;
  border-color: #FF4444;
}

.action-btn .btn-text {
  font-size: 24rpx;
  color: #666;
  position: relative;
  top: -55rpx;
}

.action-btn.primary .btn-text {
  color: white;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: #4169E1;
  color: white;
  padding: 15rpx 40rpx;
  border-radius: 25rpx;
}

.empty-btn .btn-text {
  font-size: 26rpx;
  color: white;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4169E1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多数据提示 */
.no-more-tip {
  text-align: center;
  padding: 40rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #999;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .container {
    width: 100%;
  }

  .order-content {
    flex-direction: column;
  }

  .order-image-container {
    width: 100%;
    height: 200rpx;
    margin-right: 0;
    margin-bottom: 20rpx;
  }

  .order-actions {
    flex-direction: column;
    gap: 10rpx;
  }

  .action-btn {
    width: 100%;
    text-align: center;
  }
}