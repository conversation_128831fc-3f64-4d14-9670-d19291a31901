/* pages/loan-consultation/loan-consultation.wxss */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 通用样式 */
.section-op {
  font-size: 36rpx;
  font-weight: bold;
  color: black;
  margin-bottom: 30rpx;
}
.section-ti {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  margin-left: 10rpx;
}

/* 贷款政策解读区域 */
.policy-section {
  background:white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  color: black;
  width: 750rpx;
  margin-left: 60rpx ;
  margin-top: -200rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.header-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.header-content {
  flex: 1;
}

.section-title {
  color: white;
  margin-bottom: 8rpx;
}

.update-time {
  font-size: 24rpx;
  opacity: 0.9;
  position: relative;
  top: 230rpx;
  left: 190rpx;
}

.policy-highlights {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.highlight-item {
  display: flex;
  align-items: center;
}

.highlight-dot {
  color: #FFD700;
  font-size: 28rpx;
  margin-right: 15rpx;
}

.highlight-text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* 资质预审功能 */
.qualification-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: 750rpx;
  margin-left: 40rpx;
  margin-top: 0rpx;
}

.qualification-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.3);
}

/* 贷款流程指导 */
.process-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: 750rpx;
  margin-top: -10rpx;
}

.process-steps {
  position: relative;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  position: relative;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  padding-top: 8rpx;
}

.step-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.step-line {
  position: absolute;
  left: 30rpx;
  top: 60rpx;
  width: 2rpx;
  height: 40rpx;
  background: #e0e0e0;
}

/* 表单样式 */
.form-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: 750rpx;
  margin-left: 40rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.form-input,
.form-picker {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-picker {
  display: flex;
  align-items: center;
}

.picker-text {
  color: #333;
}

.form-switch {
  transform: scale(1.2);
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #ccc;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

.submit-btn.active {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  box-shadow: 0 4rpx 16rpx rgba(255, 68, 68, 0.3);
}

/* FAQ常见问题区域 */
.faq-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: 750rpx;
  margin-top: -10rpx;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.faq-item {
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
}

.faq-question {
  padding: 25rpx;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.question-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer.show {
  max-height: 200rpx;
}

.answer-text {
  display: block;
  padding: 25rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: white;
}

/* 弹窗通用样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.qualification-modal {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  margin: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 50rpx;
  height: 50rpx;
  background: rgba(0, 0, 0, 0.1);
  color: #999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}

.modal-content {
  padding: 30rpx;
}

.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.result-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.result-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.result-value.success {
  color: #4CAF50;
}

.result-value.error {
  color: #F44336;
}

.suggestions {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.suggestions-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.suggestion-item {
  margin-bottom: 10rpx;
}

.suggestion-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  padding: 20rpx 30rpx 30rpx;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #4169E1 0%, #1E90FF 100%);
  color: white;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(65, 105, 225, 0.3);
}

/* 提交咨询半屏弹窗 */
.submit-modal {
  width: 100%;
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 拖拽指示器 */
.modal-handle {
  width: 80rpx;
  height: 8rpx;
  background: #e0e0e0;
  border-radius: 4rpx;
  margin: 20rpx auto 10rpx;
}

.submit-content {
  padding: 20rpx 30rpx 40rpx;
}

/* 成功状态头部 */
.success-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.success-icon {
  font-size: 80rpx;
  margin-bottom: 15rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 10rpx;
}

.success-subtitle {
  font-size: 26rpx;
  color: #666;
}

/* 咨询编号区域 */
.consultation-id-section {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.id-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.id-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  font-family: monospace;
}

.copy-btn {
  background: #4169E1;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 二维码区域 */
.qrcode-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.qrcode-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.qrcode-container {
  position: relative;
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

.qrcode-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-text {
  color: white;
  font-size: 24rpx;
}

/* 服务承诺 */
.service-promise {
  background: linear-gradient(135deg, #E8F5E8 0%, #F0F8FF 100%);
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
}

.promise-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.promise-item:last-child {
  margin-bottom: 0;
}

.promise-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.promise-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
}

.save-qr-btn,
.close-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
}

.save-qr-btn {
  background: linear-gradient(135deg, #4169E1 0%, #1E90FF 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(65, 105, 225, 0.3);
}

.close-btn {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}
