<!--pages/my-orders/my-orders.wxml-->
<view class="container">

  <!-- 订单状态筛选栏 -->
  <view class="order-tabs">
    <view class="tab-item {{currentTab === item.id ? 'active' : ''}}"
          wx:for="{{orderTabs}}"
          wx:key="id"
          bindtap="onTabChange"
          data-id="{{item.id}}">
      <text class="tab-text">{{item.name}}</text>
      <text class="tab-count" wx:if="{{item.count > 0}}">({{item.count}})</text>
    </view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list" wx:if="{{!showEmpty}}">
    <view class="order-card"
          wx:for="{{filteredOrderList}}"
          wx:key="id"
          bindtap="onOrderTap"
          data-id="{{item.id}}">

      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-id">订单号：{{item.id}}</text>
          <text class="order-time">{{item.createTime}}</text>
        </view>
        <view class="order-status" style="color: {{item.statusColor}};">
          {{item.statusText}}
        </view>
      </view>

      <!-- 订单内容 -->
      <view class="order-content">
        <view class="order-image-container">
          <image class="order-image" src="{{item.image}}" mode="aspectFill"></image>
        </view>

        <view class="order-details">
          <text class="order-title">{{item.title}}</text>
          <text class="order-description">{{item.description}}</text>
          <view class="order-amount">
            <text class="amount-symbol">¥</text>
            <text class="amount-value">{{item.amount}}</text>
          </view>
        </view>
      </view>

      <!-- 订单操作按钮 -->
      <view class="order-actions" wx:if="{{item.actions.length > 0}}">
        <view class="action-btn {{action === 'pay' ? 'primary' : ''}}"
              wx:for="{{item.actions}}"
              wx:key="index"
              wx:for-item="action"
              bindtap="onOrderAction"
              data-action="{{action}}"
              data-order-id="{{item.id}}"
              catchtap="">
          <text class="btn-text">
            {{action === 'pay' ? '立即支付' :
              action === 'cancel' ? '取消订单' :
              action === 'view' ? '查看详情' :
              action === 'download' ? '下载报告' :
              action === 'reorder' ? '重新下单' : '操作'}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{showEmpty}}">
    <image class="empty-icon" src="/image/empty-order.png" mode="aspectFit"></image>
    <text class="empty-text">{{emptyText}}</text>
    <view class="empty-btn" bindtap="goShopping">
      <text class="btn-text">去下单</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据提示 -->
  <view class="no-more-tip" wx:if="{{!hasMore && !loading && !showEmpty}}">
    <text class="tip-text">没有更多订单了</text>
  </view>

</view>