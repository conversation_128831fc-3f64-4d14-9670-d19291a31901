<!--pages/calculation-result/calculation-result.wxml-->
<view class="container">
  <!-- 顶部切换标签 -->
  <view class="tab-container">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      <text class="tab-text">等额本息</text>
      <view class="tab-underline" wx:if="{{currentTab === 0}}"></view>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      <text class="tab-text">等额本金</text>
      <view class="tab-underline" wx:if="{{currentTab === 1}}"></view>
    </view>
  </view>

  <!-- 计算结果卡片 -->
  <view class="result-card">
    <view class="result-header">
      <view class="loan-type-container">
        <text class="loan-type-text">
          {{loanType === 'commercial' ? '商业贷款' : ''}}
          {{loanType === 'fund' ? '公积金贷款' : ''}}
          {{loanType === 'combined' ? '组合贷款' : ''}}
        </text>
      </view>
      <text class="payment-type-text">{{currentTab === 0 ? '每月应还' : '首月应还'}}</text>
    </view>
    <view class="result-amount">
      <view class="amount-container">
        <text class="amount-number">{{monthlyPayment}}</text>
        <text class="amount-unit">元</text>
        <view class="help-icon" bindtap="showHelpModal">
          <text class="help-text">?</text>
        </view>
      </view>
    </view>
    <!-- 卡片内说明文字 -->
    <view class="card-notice-text" wx:if="{{currentTab === 1}}">
      <text class="card-notice-highlight">{{currentTab === 0 ? '每月还款金额不变' : '每月递减约' + monthlyDecrease + '元'}}</text>
    </view>
    <view class="result-details">
      <view class="detail-item">
        <text class="detail-label">贷款总额</text>
        <text class="detail-value">{{loanAmount}}万</text>
      </view>
      <view class="detail-divider"></view>
      <view class="detail-item">
        <text class="detail-label">利息总额</text>
        <text class="detail-value">{{totalInterest}}万</text>
      </view>
      <view class="detail-divider"></view>
      <view class="detail-item">
        <text class="detail-label">贷款年限</text>
        <text class="detail-value">{{loanYears}}年</text>
      </view>
    </view>
  </view>

  <!-- 组合贷款详细信息 -->
  <view wx:if="{{loanType === 'combined'}}" class="combined-details">
    <!-- 分项贷款信息 -->
   

    <!-- 组合贷款汇总卡片 -->
   
  </view>

  <!-- 说明文字 -->
  <view class="notice-text">
    <text class="notice-highlight">{{currentTab === 0 ? '每月还款金额不变' : '每月还款金额递减约' + monthlyDecrease + '元'}}</text>
    <text class="notice-content">{{currentTab === 0 ? '，其中本金的本金逐月递增，利息逐月递减。' : '，利息逐月递减，首月还款金额最高。'}}计算结果仅供参考。</text>
    <!-- 等额本金模式下的递减金额详细提示 -->
    <text wx:if="{{currentTab === 1}}" class="notice-decrease">每月递减约{{monthlyDecrease}}元</text>
  </view>

  <!-- 还款明细表格 -->
  <view class="table-container">
    <!-- 表格头部 -->
    <view class="table-header">
      <view class="table-cell header-cell">期数</view>
      <view class="table-cell header-cell">月供总额</view>
      <view class="table-cell header-cell">月供本金</view>
      <view class="table-cell header-cell">月供利息</view>
      <view class="table-cell header-cell">剩余本金</view>
    </view>
    
    <!-- 表格内容 -->
    <scroll-view class="table-body" scroll-y="true">
      <view class="table-row" wx:for="{{paymentSchedule}}" wx:key="index">
        <view class="table-cell">{{item.period}}</view>
        <view class="table-cell">{{item.monthlyPayment}}</view>
        <view class="table-cell">{{item.principal}}</view>
        <view class="table-cell">{{item.interest}}</view>
        <view class="table-cell">{{item.remainingPrincipal}}</view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部按钮区域 -->
  <view class="bottom-actions">
    <view class="action-item" bindtap="onlineConsult">
      <image class="action-icon" src="/images/consult-icon.png" mode="aspectFit"></image>
      <text class="action-text">在线咨询</text>
    </view>
    <button class="phone-consult-btn" bindtap="phoneConsult">电话咨询</button>
  </view>
</view>
