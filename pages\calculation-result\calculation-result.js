// pages/calculation-result/calculation-result.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 当前选中的标签页 (0: 等额本息, 1: 等额本金)
    currentTab: 0,

    // 贷款类型：commercial-商业贷款，fund-公积金贷款，combined-组合贷款
    loanType: 'commercial',

    // 计算参数
    loanAmount: 0,      // 贷款总额(万元)
    loanYears: 0,       // 贷款年限
    interestRate: 0,    // 年利率

    // 组合贷款参数
    commercialAmount: 0,    // 商业贷款金额
    fundAmount: 0,          // 公积金贷款金额
    commercialRate: 0,      // 商业贷款利率
    fundRate: 0,            // 公积金贷款利率

    // 计算结果
    monthlyPayment: '0.00',     // 每月应还金额
    totalInterest: '0.00',      // 利息总额
    totalPayment: '0.00',       // 还款总额
    monthlyDecrease: '0.00',    // 月供递减金额（等额本金模式）

    // 组合贷款结果
    commercialMonthlyPayment: '0.00',
    commercialTotalInterest: '0.00',
    fundMonthlyPayment: '0.00',
    fundTotalInterest: '0.00',
    totalMonthlyPayment: '0.00',
    totalCombinedInterest: '0.00',

    // 还款明细
    paymentSchedule: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('房贷计算结果页面加载', options);

    // 获取贷款类型
    const loanType = options.loanType || 'commercial';
    this.setData({ loanType: loanType });

    if (loanType === 'combined') {
      // 组合贷款参数处理
      if (options.loanYears && (options.commercialAmount || options.fundAmount)) {
        const loanYears = parseInt(options.loanYears);
        const commercialAmount = parseFloat(options.commercialAmount || 0);
        const fundAmount = parseFloat(options.fundAmount || 0);
        const commercialRate = parseFloat(options.commercialRate || 0);
        const fundRate = parseFloat(options.fundRate || 0);

        this.setData({
          loanYears: loanYears,
          commercialAmount: commercialAmount,
          fundAmount: fundAmount,
          commercialRate: commercialRate,
          fundRate: fundRate
        });

        // 执行组合贷款计算
        this.calculateCombinedLoan();
      } else {
        wx.showToast({
          title: '组合贷款参数错误',
          icon: 'none'
        });
      }
    } else {
      // 单一贷款类型参数处理
      if (options.loanAmount && options.loanYears && options.interestRate) {
        const loanAmount = parseFloat(options.loanAmount);
        const loanYears = parseInt(options.loanYears);
        const interestRate = parseFloat(options.interestRate);

        this.setData({
          loanAmount: loanAmount,
          loanYears: loanYears,
          interestRate: interestRate
        });

        // 执行单一贷款计算
        this.calculateLoan();
      } else {
        wx.showToast({
          title: '计算参数错误',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);

    // 如果是同一个标签，不需要切换
    if (tab === this.data.currentTab) {
      return;
    }

    console.log('切换标签页:', tab === 0 ? '等额本息' : '等额本金');

    // 先更新标签状态
    this.setData({
      currentTab: tab
    });

    // 延迟一帧后重新计算，确保UI更新流畅
    wx.nextTick(() => {
      // 根据贷款类型调用不同的计算函数
      if (this.data.loanType === 'combined') {
        this.calculateCombinedLoan();
      } else {
        this.calculateLoan();
      }
    });
  },

  /**
   * 组合贷款计算
   */
  calculateCombinedLoan() {
    const { commercialAmount, fundAmount, commercialRate, fundRate, loanYears, currentTab } = this.data;

    console.log('开始计算组合贷款', {
      commercialAmount,
      fundAmount,
      commercialRate,
      fundRate,
      loanYears,
      currentTab
    });

    let commercialMonthly = 0, commercialInterest = 0;
    let fundMonthly = 0, fundInterest = 0;

    // 计算商业贷款部分
    if (commercialAmount > 0) {
      const result = this.calculateSingleLoan(commercialAmount, commercialRate, loanYears, currentTab);
      commercialMonthly = result.monthlyPayment;
      commercialInterest = result.totalInterest;
    }

    // 计算公积金贷款部分
    if (fundAmount > 0) {
      const result = this.calculateSingleLoan(fundAmount, fundRate, loanYears, currentTab);
      fundMonthly = result.monthlyPayment;
      fundInterest = result.totalInterest;
    }

    // 汇总结果
    const totalMonthly = commercialMonthly + fundMonthly;
    const totalInterest = commercialInterest + fundInterest;
    const totalAmount = commercialAmount + fundAmount;
    const totalPaymentAmount = totalAmount + totalInterest;

    this.setData({
      loanAmount: totalAmount.toFixed(2),
      monthlyPayment: totalMonthly.toFixed(2),
      totalInterest: totalInterest.toFixed(2),
      totalPayment: totalPaymentAmount.toFixed(2),
      commercialMonthlyPayment: commercialMonthly.toFixed(2),
      commercialTotalInterest: commercialInterest.toFixed(2),
      fundMonthlyPayment: fundMonthly.toFixed(2),
      fundTotalInterest: fundInterest.toFixed(2),
      totalLoanAmount: totalAmount.toFixed(2),
      totalMonthlyPayment: totalMonthly.toFixed(2),
      totalCombinedInterest: totalInterest.toFixed(2)
    });

    // 生成还款明细（以总贷款为基础）
    this.generateCombinedPaymentSchedule();
  },

  /**
   * 单笔贷款计算（用于组合贷款）
   */
  calculateSingleLoan(amount, rate, years, paymentType) {
    // 参数验证
    if (!amount || !rate || !years || amount <= 0 || rate <= 0 || years <= 0) {
      console.error('单笔贷款计算参数错误:', { amount, rate, years, paymentType });
      return {
        monthlyPayment: 0,
        totalInterest: 0
      };
    }

    const principal = amount * 10000; // 转换为元
    const monthlyRate = rate / 100 / 12; // 月利率
    const totalMonths = years * 12; // 总月数

    let monthlyPayment, totalInterest;

    if (paymentType === 0) {
      // 等额本息
      monthlyPayment = principal * monthlyRate * Math.pow(1 + monthlyRate, totalMonths) /
                      (Math.pow(1 + monthlyRate, totalMonths) - 1);
      totalInterest = monthlyPayment * totalMonths - principal;
    } else {
      // 等额本金
      const monthlyPrincipal = principal / totalMonths;
      const firstMonthInterest = principal * monthlyRate;
      monthlyPayment = monthlyPrincipal + firstMonthInterest; // 首月还款
      totalInterest = principal * monthlyRate * (totalMonths + 1) / 2;
    }

    return {
      monthlyPayment: monthlyPayment,
      totalInterest: totalInterest / 10000 // 转换为万元
    };
  },

  /**
   * 房贷计算主函数
   */
  calculateLoan() {
    const { loanAmount, loanYears, interestRate, currentTab } = this.data;
    const totalMonths = loanYears * 12;

    console.log('开始计算房贷 - 模式:', currentTab === 0 ? '等额本息' : '等额本金', '总期数:', totalMonths);

    // 先清空表格数据，避免闪烁
    this.setData({
      paymentSchedule: []
    });

    // 如果期数较多，显示加载提示
    if (totalMonths > 120) { // 10年以上
      wx.showLoading({
        title: '计算中...',
        mask: true
      });
    }

    // 延迟计算，确保UI更新流畅
    setTimeout(() => {
      try {
        if (currentTab === 0) {
          // 等额本息计算
          this.calculateEqualPayment();
        } else {
          // 等额本金计算
          this.calculateEqualPrincipal();
        }
      } finally {
        // 隐藏加载提示
        if (totalMonths > 120) {
          wx.hideLoading();
        }
      }
    }, 50);
  },

  /**
   * 等额本息计算
   */
  calculateEqualPayment() {
    const { loanAmount, loanYears, interestRate } = this.data;

    // 参数验证
    if (!loanAmount || !loanYears || !interestRate || loanAmount <= 0 || loanYears <= 0 || interestRate <= 0) {
      console.error('等额本息计算参数错误:', { loanAmount, loanYears, interestRate });
      this.setData({
        monthlyPayment: '0.00',
        totalInterest: '0.00',
        totalPayment: '0.00',
        monthlyDecrease: '0.00',
        paymentSchedule: []
      });
      return;
    }

    const principal = loanAmount * 10000; // 转换为元
    const monthlyRate = interestRate / 100 / 12; // 月利率
    const totalMonths = loanYears * 12; // 总月数
    
    // 计算每月还款额
    const monthlyPayment = principal * monthlyRate * Math.pow(1 + monthlyRate, totalMonths) / 
                          (Math.pow(1 + monthlyRate, totalMonths) - 1);
    
    // 计算总利息
    const totalPayment = monthlyPayment * totalMonths;
    const totalInterest = totalPayment - principal;
    
    // 生成还款明细（显示完整期数）
    const schedule = [];
    let remainingPrincipal = principal;

    console.log('等额本息计算 - 总期数:', totalMonths);

    for (let i = 1; i <= totalMonths; i++) {
      const interestPayment = remainingPrincipal * monthlyRate;
      const principalPayment = monthlyPayment - interestPayment;
      remainingPrincipal -= principalPayment;

      schedule.push({
        period: i,
        monthlyPayment: monthlyPayment.toFixed(2),
        principal: principalPayment.toFixed(2),
        interest: interestPayment.toFixed(2),
        remainingPrincipal: Math.max(0, remainingPrincipal).toFixed(2) // 避免负数
      });
    }

    console.log('等额本息 - 生成还款明细条数:', schedule.length);
    
    this.setData({
      monthlyPayment: monthlyPayment.toFixed(2),
      totalInterest: (totalInterest / 10000).toFixed(2),
      totalPayment: (totalPayment / 10000).toFixed(2),
      monthlyDecrease: '0.00', // 等额本息模式下递减金额为0
      paymentSchedule: schedule
    });
  },

  /**
   * 生成组合贷款还款明细
   */
  generateCombinedPaymentSchedule() {
    const { commercialAmount, fundAmount, commercialRate, fundRate, loanYears, currentTab } = this.data;
    const totalMonths = loanYears * 12;
    const schedule = [];

    for (let i = 1; i <= totalMonths; i++) {
      let totalMonthlyPayment = 0;
      let totalPrincipal = 0;
      let totalInterest = 0;
      let totalRemaining = 0;

      // 计算商业贷款部分
      if (commercialAmount > 0) {
        const commercial = this.calculateMonthlyDetail(commercialAmount, commercialRate, loanYears, i, currentTab);
        totalMonthlyPayment += commercial.monthlyPayment;
        totalPrincipal += commercial.principal;
        totalInterest += commercial.interest;
        totalRemaining += commercial.remaining;
      }

      // 计算公积金贷款部分
      if (fundAmount > 0) {
        const fund = this.calculateMonthlyDetail(fundAmount, fundRate, loanYears, i, currentTab);
        totalMonthlyPayment += fund.monthlyPayment;
        totalPrincipal += fund.principal;
        totalInterest += fund.interest;
        totalRemaining += fund.remaining;
      }

      schedule.push({
        period: i,
        monthlyPayment: totalMonthlyPayment.toFixed(2),
        principal: totalPrincipal.toFixed(2),
        interest: totalInterest.toFixed(2),
        remainingPrincipal: totalRemaining.toFixed(2)
      });
    }

    this.setData({
      paymentSchedule: schedule
    });
  },

  /**
   * 计算单月还款详情
   */
  calculateMonthlyDetail(amount, rate, years, period, paymentType) {
    const principal = amount * 10000;
    const monthlyRate = rate / 100 / 12;
    const totalMonths = years * 12;

    if (paymentType === 0) {
      // 等额本息
      const monthlyPayment = principal * monthlyRate * Math.pow(1 + monthlyRate, totalMonths) /
                            (Math.pow(1 + monthlyRate, totalMonths) - 1);

      // 计算到当前期数的剩余本金
      let remainingPrincipal = principal;
      for (let i = 1; i < period; i++) {
        const interestPayment = remainingPrincipal * monthlyRate;
        const principalPayment = monthlyPayment - interestPayment;
        remainingPrincipal -= principalPayment;
      }

      const interestPayment = remainingPrincipal * monthlyRate;
      const principalPayment = monthlyPayment - interestPayment;
      remainingPrincipal -= principalPayment;

      return {
        monthlyPayment: monthlyPayment,
        principal: principalPayment,
        interest: interestPayment,
        remaining: Math.max(0, remainingPrincipal)
      };
    } else {
      // 等额本金
      const monthlyPrincipal = principal / totalMonths;
      const remainingPrincipal = principal - monthlyPrincipal * (period - 1);
      const interestPayment = remainingPrincipal * monthlyRate;
      const monthlyPayment = monthlyPrincipal + interestPayment;

      return {
        monthlyPayment: monthlyPayment,
        principal: monthlyPrincipal,
        interest: interestPayment,
        remaining: Math.max(0, remainingPrincipal - monthlyPrincipal)
      };
    }
  },

  /**
   * 等额本金计算
   */
  calculateEqualPrincipal() {
    const { loanAmount, loanYears, interestRate } = this.data;

    // 参数验证
    if (!loanAmount || !loanYears || !interestRate || loanAmount <= 0 || loanYears <= 0 || interestRate <= 0) {
      console.error('等额本金计算参数错误:', { loanAmount, loanYears, interestRate });
      this.setData({
        monthlyPayment: '0.00',
        totalInterest: '0.00',
        totalPayment: '0.00',
        monthlyDecrease: '0.00',
        paymentSchedule: []
      });
      return;
    }

    const principal = loanAmount * 10000; // 转换为元
    const monthlyRate = interestRate / 100 / 12; // 月利率
    const totalMonths = loanYears * 12; // 总月数

    // 每月应还本金
    const monthlyPrincipal = principal / totalMonths;

    console.log('等额本金计算 - 总期数:', totalMonths);

    // 计算总利息（使用数学公式，避免循环）
    const totalInterestSum = principal * monthlyRate * (totalMonths + 1) / 2;

    // 首月还款金额
    const firstMonthPayment = monthlyPrincipal + principal * monthlyRate;

    // 计算月供递减金额（每月递减的利息）
    const monthlyDecrease = monthlyPrincipal * monthlyRate;

    console.log('等额本金 - 首月还款:', firstMonthPayment.toFixed(2));
    console.log('等额本金 - 月供递减金额:', monthlyDecrease.toFixed(2));
    console.log('等额本金 - 总利息:', (totalInterestSum / 10000).toFixed(2));

    // 先设置基本结果，不生成完整明细表
    this.setData({
      monthlyPayment: firstMonthPayment.toFixed(2),
      totalInterest: (totalInterestSum / 10000).toFixed(2),
      totalPayment: ((principal + totalInterestSum) / 10000).toFixed(2),
      monthlyDecrease: monthlyDecrease.toFixed(2),
      paymentSchedule: [] // 先清空明细表
    });

    // 异步生成还款明细（只显示前24期，避免性能问题）
    setTimeout(() => {
      this.generateEqualPrincipalSchedule();
    }, 100);
  },

  /**
   * 生成等额本金还款明细（限制显示期数）
   */
  generateEqualPrincipalSchedule() {
    const { loanAmount, loanYears, interestRate } = this.data;

    const principal = loanAmount * 10000;
    const monthlyRate = interestRate / 100 / 12;
    const totalMonths = loanYears * 12;
    const monthlyPrincipal = principal / totalMonths;

    // 生成完整的还款明细表
    const schedule = [];
    let remainingPrincipal = principal;

    console.log('开始生成等额本金还款明细，总期数:', totalMonths);

    for (let i = 1; i <= totalMonths; i++) {
      const interestPayment = remainingPrincipal * monthlyRate;
      const monthlyPayment = monthlyPrincipal + interestPayment;
      remainingPrincipal -= monthlyPrincipal;

      schedule.push({
        period: i,
        monthlyPayment: monthlyPayment.toFixed(2),
        principal: monthlyPrincipal.toFixed(2),
        interest: interestPayment.toFixed(2),
        remainingPrincipal: Math.max(0, remainingPrincipal).toFixed(2)
      });
    }

    console.log('等额本金 - 生成还款明细条数:', schedule.length);

    this.setData({
      paymentSchedule: schedule
    });
  },

  /**
   * 在线咨询
   */
  onlineConsult() {
    wx.showToast({
      title: '在线咨询功能开发中',
      icon: 'none'
    });
  },

  /**
   * 电话咨询
   */
  phoneConsult() {
    wx.showModal({
      title: '电话咨询',
      content: '是否拨打客服电话：************？',
      confirmText: '拨打',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001234567',
            success: () => {
              console.log('拨打电话成功');
            },
            fail: (err) => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 显示帮助页面
   */
  showHelpModal() {
    console.log('跳转到帮助页面');
    wx.navigateTo({
      url: '/pages/help/help',
      success: () => {
        console.log('跳转帮助页面成功');
      },
      fail: (err) => {
        console.error('跳转帮助页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '锦绣资产 - 房贷计算结果',
      path: '/pages/calculation-result/calculation-result'
    };
  }
});
