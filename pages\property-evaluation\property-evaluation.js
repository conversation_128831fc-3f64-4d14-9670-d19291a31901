// pages/property-evaluation/property-evaluation.js
Page({
  data: {
    currentStep: 1,
    totalSteps: 5,
    progressWidth: 20, // 初始进度宽度 (1/5 * 100 = 20%)
    
    // 表单数据
    formData: {
      // 基本信息
      communityName: '',
      address: '',
      houseType: '',
      buildingArea: '',
      houseAge: '',
      decoration: '',

      // 位置信息
      currentFloor: '',
      totalFloor: '',
      orientation: '',

      // 配套设施
      facilities: [],
      houseStatus: '',
      specialConditions: [],

      // 图片信息
      housePhotos: [],
      floorPlan: '',
      propertyCard: ''
    },

    // 选择器索引
    pickerIndexes: {
      houseType: -1,
      houseAge: -1,
      decoration: -1,
      orientation: -1,
      houseStatus: -1
    },
    
    // 选择器选项
    houseTypeOptions: ['1室1厅', '2室1厅', '2室2厅', '3室1厅', '3室2厅', '4室2厅', '4室3厅', '5室及以上'],
    houseAgeOptions: ['1年内', '1-3年', '3-5年', '5-10年', '10-15年', '15年以上'],
    decorationOptions: ['毛坯', '简装', '精装', '豪装'],
    orientationOptions: ['南北通透', '南向', '北向', '东向', '西向', '东南', '西南', '东北', '西北'],
    facilitiesOptions: [
      { id: 'subway', name: '地铁', selected: false },
      { id: 'school', name: '学校', selected: false },
      { id: 'hospital', name: '医院', selected: false },
      { id: 'mall', name: '商场', selected: false },
      { id: 'park', name: '公园', selected: false },
      { id: 'bus', name: '公交', selected: false }
    ],
    houseStatusOptions: ['自住', '出租', '空置'],
    specialConditionsOptions: [
      { id: 'topFloor', name: '顶楼', selected: false },
      { id: 'bottomFloor', name: '底楼', selected: false },
      { id: 'streetFacing', name: '临街', selected: false },
      { id: 'poorLight', name: '采光不足', selected: false },
      { id: 'noElevator', name: '无电梯', selected: false },
      { id: 'noisePollution', name: '噪音污染', selected: false }
    ]
  },

  onLoad: function (options) {
    console.log('房产评估页面加载', options);

    // 初始化进度条
    this.updateProgressWidth();

    // 检查是否是重新开始评估
    if (options.restart === 'true') {
      // 清除本地保存的数据
      wx.removeStorageSync('propertyEvaluationData');
      console.log('重新开始评估，清除保存数据');
    } else if (options.continue === 'true') {
      // 继续之前的进度
      this.loadSavedDataWithStep();
    } else {
      // 默认情况：加载表单数据但从第1步开始
      this.loadSavedData();
    }
  },

  // 更新进度条宽度
  updateProgressWidth: function() {
    const { currentStep, totalSteps } = this.data;

    // 确保数据类型正确且避免除零
    const step = parseInt(currentStep) || 1;
    const total = parseInt(totalSteps) || 5;

    // 计算进度百分比，保留一位小数
    const progressWidth = Math.round((step / total) * 100 * 10) / 10;

    this.setData({
      progressWidth: Math.min(Math.max(progressWidth, 0), 100) // 限制在0-100之间
    });

    console.log(`进度更新: ${step}/${total} = ${progressWidth}%`);
  },

  // 加载本地保存的数据
  loadSavedData: function() {
    try {
      const savedData = wx.getStorageSync('propertyEvaluationData');
      if (savedData) {
        // 恢复选择器索引
        const pickerIndexes = { ...this.data.pickerIndexes };
        if (savedData.formData) {
          // 根据保存的值计算索引
          if (savedData.formData.houseType) {
            pickerIndexes.houseType = this.data.houseTypeOptions.indexOf(savedData.formData.houseType);
          }
          if (savedData.formData.houseAge) {
            pickerIndexes.houseAge = this.data.houseAgeOptions.indexOf(savedData.formData.houseAge);
          }
          if (savedData.formData.decoration) {
            pickerIndexes.decoration = this.data.decorationOptions.indexOf(savedData.formData.decoration);
          }
          if (savedData.formData.orientation) {
            pickerIndexes.orientation = this.data.orientationOptions.indexOf(savedData.formData.orientation);
          }
          if (savedData.formData.houseStatus) {
            pickerIndexes.houseStatus = this.data.houseStatusOptions.indexOf(savedData.formData.houseStatus);
          }
        }

        this.setData({
          formData: savedData.formData || this.data.formData,
          // 不恢复currentStep，始终从第1步开始
          pickerIndexes: pickerIndexes
        });
        this.updateProgressWidth(); // 更新进度条
        console.log('加载保存的数据:', savedData);
      }
    } catch (e) {
      console.error('加载保存数据失败:', e);
    }
  },

  // 加载本地保存的数据（包含步骤）
  loadSavedDataWithStep: function() {
    try {
      const savedData = wx.getStorageSync('propertyEvaluationData');
      if (savedData) {
        // 恢复选择器索引
        const pickerIndexes = { ...this.data.pickerIndexes };
        if (savedData.formData) {
          // 根据保存的值计算索引
          if (savedData.formData.houseType) {
            pickerIndexes.houseType = this.data.houseTypeOptions.indexOf(savedData.formData.houseType);
          }
          if (savedData.formData.houseAge) {
            pickerIndexes.houseAge = this.data.houseAgeOptions.indexOf(savedData.formData.houseAge);
          }
          if (savedData.formData.decoration) {
            pickerIndexes.decoration = this.data.decorationOptions.indexOf(savedData.formData.decoration);
          }
          if (savedData.formData.orientation) {
            pickerIndexes.orientation = this.data.orientationOptions.indexOf(savedData.formData.orientation);
          }
          if (savedData.formData.houseStatus) {
            pickerIndexes.houseStatus = this.data.houseStatusOptions.indexOf(savedData.formData.houseStatus);
          }
        }

        this.setData({
          formData: savedData.formData || this.data.formData,
          currentStep: savedData.currentStep || 1, // 恢复步骤
          pickerIndexes: pickerIndexes
        });
        this.updateProgressWidth(); // 更新进度条
        console.log('加载保存的数据（包含步骤）:', savedData);
      }
    } catch (e) {
      console.error('加载保存数据失败:', e);
    }
  },

  // 保存数据到本地
  saveDataToLocal: function() {
    try {
      wx.setStorageSync('propertyEvaluationData', {
        formData: this.data.formData,
        currentStep: this.data.currentStep,
        timestamp: Date.now()
      });
    } catch (e) {
      console.error('保存数据失败:', e);
    }
  },

  // 下一步
  nextStep: function() {
    if (this.validateCurrentStep()) {
      if (this.data.currentStep < this.data.totalSteps) {
        this.setData({
          currentStep: this.data.currentStep + 1
        });
        this.updateProgressWidth(); // 更新进度条
        this.saveDataToLocal();
      } else {
        // 最后一步，跳转到结果页面
        this.submitEvaluation();
      }
    }
  },

  // 上一步
  prevStep: function() {
    if (this.data.currentStep > 1) {
      this.setData({
        currentStep: this.data.currentStep - 1
      });
      this.updateProgressWidth(); // 更新进度条
    }
  },

  // 验证当前步骤
  validateCurrentStep: function() {
    const { currentStep, formData } = this.data;
    
    switch (currentStep) {
      case 1:
        if (!formData.communityName || !formData.houseType || !formData.buildingArea) {
          wx.showToast({
            title: '请完善基本信息',
            icon: 'none'
          });
          return false;
        }
        break;
      case 2:
        if (!formData.houseAge || !formData.decoration) {
          wx.showToast({
            title: '请完善房屋信息',
            icon: 'none'
          });
          return false;
        }
        break;
      case 3:
        if (!formData.currentFloor || !formData.totalFloor || !formData.orientation) {
          wx.showToast({
            title: '请完善位置信息',
            icon: 'none'
          });
          return false;
        }
        break;
      case 4:
        if (!formData.houseStatus) {
          wx.showToast({
            title: '请选择房屋现状',
            icon: 'none'
          });
          return false;
        }
        break;
    }
    return true;
  },

  // 提交评估
  submitEvaluation: function() {
    wx.showLoading({
      title: '评估中...'
    });

    // 模拟评估计算
    setTimeout(() => {
      wx.hideLoading();
      
      // 跳转到结果页面
      wx.navigateTo({
        url: '/pages/property-evaluation/result?data=' + encodeURIComponent(JSON.stringify(this.data.formData))
      });
    }, 2000);
  },

  // 输入事件处理
  onInput: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择器变化事件
  onPickerChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const options = e.currentTarget.dataset.options;
    const index = parseInt(e.detail.value);
    const value = this.data[options][index];

    this.setData({
      [`formData.${field}`]: value,
      [`pickerIndexes.${field}`]: index
    });
  },

  // 多选项切换
  onToggleMultiSelect: function(e) {
    const field = e.currentTarget.dataset.field;
    const optionsField = e.currentTarget.dataset.options;
    const index = e.currentTarget.dataset.index;

    const options = this.data[optionsField];
    options[index].selected = !options[index].selected;

    // 更新选中的值
    const selectedValues = options.filter(item => item.selected).map(item => item.id);

    this.setData({
      [optionsField]: options,
      [`formData.${field}`]: selectedValues
    });
  },

  // 选择图片
  chooseImage: function(e) {
    const type = e.currentTarget.dataset.type;
    const maxCount = type === 'housePhotos' ? 6 - this.data.formData.housePhotos.length : 1;

    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;

        if (type === 'housePhotos') {
          const currentPhotos = this.data.formData.housePhotos;
          const newPhotos = currentPhotos.concat(tempFilePaths);
          this.setData({
            'formData.housePhotos': newPhotos
          });
        } else {
          this.setData({
            [`formData.${type}`]: tempFilePaths[0]
          });
        }

        this.saveDataToLocal();
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;

    if (type === 'housePhotos') {
      const photos = this.data.formData.housePhotos;
      photos.splice(index, 1);
      this.setData({
        'formData.housePhotos': photos
      });
    } else {
      this.setData({
        [`formData.${type}`]: ''
      });
    }

    this.saveDataToLocal();
  },

  // 预览图片
  previewImage: function(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;

    let urls = [];
    let current = '';

    if (type === 'housePhotos') {
      urls = this.data.formData.housePhotos;
      current = urls[index];
    } else {
      urls = [this.data.formData[type]];
      current = urls[0];
    }

    wx.previewImage({
      urls: urls,
      current: current
    });
  }
});
