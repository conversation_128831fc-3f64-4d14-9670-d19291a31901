/* pages/calculator/calculator.wxss */

/* 页面容器 */
.container {
  width: 750rpx;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 主要内容区域 */
.content {
  width: 750rpx;
  padding: 0 30rpx;
  margin-top: -200rpx;
  box-sizing: border-box;
}

/* 选项卡 */
.tab-container {
  display: flex;
  background-color: white;
  box-sizing: border-box;
  position: relative;
  width: 750rpx;
  margin-left:-20rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 32rpx;
  color: #666666;
  transition: all 0.3s ease;
  position: relative;
  background-color: white;
}

.tab-item.active {
  color: #FF4444;
  font-weight: 500;
  background-color: white;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 6rpx;
  background-color: #FF4444;
  border-radius: 3rpx;
}

/* 表单区域 */
.form-container {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
  width: 750rpx;
  box-sizing: border-box;
  margin-left: -30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 确保所有表单项都有下划线 */
.form-item:last-child {
  border-bottom: 1rpx solid #f0f0f0;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.form-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.form-input {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  flex: 1;
  margin-right: 10rpx;
  margin-right: 30rpx;
}

.form-unit {
  font-size: 32rpx;
  color: #333;
}

.form-value-container {
  display: flex;
  align-items: center;
}
.valueo{
  font-size: 32rpx;
  color: #666;
  position: relative;
  left: -20rpx;
}
.form-unitop{
  font-size: 32rpx;
  color: #666;
  position: relative;
  left: -10rpx;
}
.form-value {
  font-size: 32rpx;
  color: #666;
  margin-right: 10rpx;
}

.form-select {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
}

.arrow-text {
  font-size: 28rpx;
  color: #999;
  margin-left: 10rpx;
}

/* LPR信息 */
.lpr-info {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  margin: 10rpx 0;
  background-color: rgba(125, 217, 230, 0.1);
  border-bottom: 1rpx solid #f0f0f0;
}

.lpr-tag {
  background-color: #FF4444;
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
}

.lpr-text {
  font-size: 24rpx;
  color: #666;
}

/* 计算按钮 */
.calculate-btn {
  background: linear-gradient(135deg, #FF4444 0%, #FF6666 100%);
  color: white;
  text-align: center;
  padding: 30rpx 0;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin: 40rpx 0;
  box-shadow: 0 8rpx 20rpx rgba(255, 68, 68, 0.3);
  width: 690rpx;
  box-sizing: border-box;
}

.calculate-btn:active {
  transform: scale(0.98);
  transition: transform 0.1s;
}

/* 常见问题链接 */
.faq-link {
  text-align: center;
  padding: 30rpx 0;
  width: 690rpx;
  box-sizing: border-box;
}

.faq-link text {
  color: #FF4444;
  font-size: 28rpx;
  text-decoration: underline;
}

/* 选择器样式 */
.picker-content {
  padding: 20rpx;
  background-color: white;
  border-radius: 10rpx;
  margin: 20rpx;
  text-align: center;
}

.picker-content text {
  font-size: 32rpx;
  color: #333;
}

